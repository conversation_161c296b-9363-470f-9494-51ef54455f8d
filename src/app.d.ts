/// See https://kit.svelte.dev/docs/types#app
/// for information about these interfaces
declare global {
	namespace App {
		interface Locals {
			userStore: any;
			sessionId: string;
		}
		// interface Error {}
		// interface Locals {}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

declare module '$env/static/private' {
	export const API_TOKEN: string;
	export const API_URL: string;
	export const CONCRETE_URL: string;
	export const COOKIE_DOMAIN: string;
}

export {};
