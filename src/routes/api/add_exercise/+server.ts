import { json } from '@sveltejs/kit';
import { addWorkoutExercise } from '$lib/services/workout_exercise.service.js';
import { addExercise, getExercise,  } from '$lib/services/exercise.services';

export async function POST({ request, locals }) {
    const body = await request.json();
    const item = body?.item;

    try {
        const exercise = await getExercise({ id: item.exercise.id, locals, fields: '*' });
        
        if (!exercise) {
            await addExercise({ exercise: item.exercise, locals });
        }

        const workouts_exercises = await addWorkoutExercise({
            workout_id: parseInt(item.workout_id),
            exercise_id: item.exercise.id,
            position: item.position
        }, locals);


        console.log(workouts_exercises);
        if(typeof workouts_exercises === 'object' && workouts_exercises.data){
                return json({success: true, data: workouts_exercises.data});

        }
    } catch (error) {
        console.log(error);
    return json({success: false });

    }



    return json({success: false });
}