import { logout } from '$lib/services/auth.services';
import { COOKIE_DOMAIN } from '$env/static/private';
import type { IAuth } from '$lib/typings';

export async function POST({ cookies, locals }) {
    // Always clear local auth data, regardless of remote logout success
    const clearLocalAuth = () => {
        // Clear session cookie with secure options
        cookies.delete('CONCRETE5', {
            path: '/',
            domain: COOKIE_DOMAIN,
            secure: true,  // Only send over HTTPS
            sameSite: 'strict',  // Protect against CSRF
            httpOnly: true  // Prevent JavaScript access
        });

        // Clear user store if it exists
        if (locals.userStore) {
            const emptyAuth: IAuth = {
                user: null,
                isPremium: false,
                access_token: null,
                refresh_token: null,
                isAuthenticated: false
            };
            locals.userStore.set(emptyAuth);
        }
    };

    try {
        // Attempt remote logout
        const mode = import.meta.env.MODE ? import.meta.env.MODE : null;
        // Attempt remote logout if not on local machine
        // This is a workaround for local development where remote logout might not be needed
        // and could cause issues with session cookies
        if (mode && mode != 'development') {
            await logout();
        }
        
        // Clear local auth data
        clearLocalAuth();

        return new Response(JSON.stringify({ success: true }), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-store, max-age=0'  // Prevent caching
            }
        });
    } catch (error) {
        console.error('Logout error:', error);
        
        // Still clear local auth data
        clearLocalAuth();

        return new Response(JSON.stringify({ success: true }), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-store, max-age=0'  // Prevent caching
            }
        });
    }
} 