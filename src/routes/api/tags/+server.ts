import { json } from '@sveltejs/kit';
import { createTag, tagExercise, editTag, deleteTag, deleteTagFromExercise, getExerciseByName, getExerciseTag } from "$lib/services/tag.service.js";

export async function GET({ url, locals }) {
    const name = url.searchParams.get('name');
    const exerciseId = url.searchParams.get('exerciseId');

    if(name){
        const data = await getExerciseByName({name, locals});
        return json(data);
    }

    if(exerciseId){
        const data = await getExerciseTag({exerciseId, locals});
        return json(data);
    }
}

export async function POST({ request, locals }) {
    const {name, color, exerciseId, tagId} = await request.json();
    if(tagId && exerciseId) {
        const data = await tagExercise({tagId, exerciseId, locals});
        return json(data);
    }
    const data = await createTag({ tag: {name, color}, locals});

    return json(data);
}

export async function PATCH({ request,locals }) {
    const {name, color, tagId} = await request.json();

    if(name && color && tagId){
        const data = await editTag({tag:{name, color}, tagId, locals});
        return json(data);
    }

}

export async function DELETE({ url, locals }) {
    const tagId = url.searchParams.get('tagId');
    const exerciseId = url.searchParams.get('exerciseId');

    if (tagId && !exerciseId){
        const data = await deleteTag({tagId, locals});
        return json(data);
    }

    if(tagId && exerciseId){
        const data = await deleteTagFromExercise({tagId, exerciseId, locals});
        return json(data);
    }
}