import { customFetch } from '$lib/fetch.js';
import { json } from '@sveltejs/kit';
import { updateK<PERSON>ban } from '$lib/services/workout_exercise.service.js';

export async function POST({ request, locals }) {
    const body = await request.json();
    const res = await update<PERSON><PERSON>ban(body?.items, locals);
    return json({success: true, ... res[0]});
}

export async function DELETE({ request, locals}) {
    const { id } = await request.json();

    const res = await customFetch({
        url: `/items/workouts_exercises/${parseInt(id)}`,
        options: {
            method: 'DELETE'
        },
        locals
    });

    if(res.status === 204){
        return json({
            success: true,
        });
    }

    return json({
        success: false,
    });
}

// export async function POST({ request, locals }) {
//     let exercise : { id: string, name: string } | null;
//     const body = await request.json();

//     if (body.exercise.workout_exercise_id) {
//         const res = await updateExerciseToAnotherWorkout({
//             locals,
//             workout_exercise_id: body.exercise.workout_exercise_id,
//             workout_id: body.workout_id,
//             exercise_id: body.exercise.id,
//             position: body.position
//         })

//         return json({ ...res });
//     }


//     exercise = await getExercise({ id: body.exercise.id, locals, fields: '*' });

    
//     if (exercise === null) {        
//         exercise = await addExercise({exercise: body.exercise, locals});
//     }

//     const workouts_exercises = await addExerciseToWorkout({
//         locals,
//         workout_id: body.workout_id,
//         exercise_id: exercise!.id,
//         position: body.position
//     });


//     return json({ workouts_exercises });
// }