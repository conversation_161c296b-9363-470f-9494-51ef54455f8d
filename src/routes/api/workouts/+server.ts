import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { addWorkAfter, addWorkout, addWorkoutBefore, getWorkouts, updateWorkouts } from '$lib/services/workout.services';

export const GET: RequestHandler = async ({ url, locals }) => {
   const programId = url.searchParams.get('program');

   if (!programId) {
      return json({
        success: false,
        message: 'No program id provided'
      })
    }

    const res = await getWorkouts({ programId, locals });

    return json({ ...res });
}

export async function POST({ request, locals }) {
  const body = await request.json();
  if(body.program_id && body.before){
    const data = await addWorkoutBefore({locals, ...body});
    return json({...data});
  }

  if(body.program_id && body.after){
    const data = await addWorkAfter({locals, ...body});
    return json({...data});
  }
  const data = await addWorkout({locals, ...body});
  return json({...data})

}

export async function PATCH({ request, locals}) {
  const body = await request.json();
  
  try {
    await updateWorkouts({items: body.items, locals});
    
    return json({
      success: true,
      message: 'Workouts update successfully'
    })
  } catch (err) {
    return json({
      success: false,
      message: 'Internal error'
    })
  }
}