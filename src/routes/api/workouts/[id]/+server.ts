import { json } from "@sveltejs/kit";
import { deleteWorkout, updateWorkout } from "$lib/services/workout.services";

export async function PATCH({ params, request, locals }) {
	const { id } = params;
	const body = await request.json();
	const data = await updateWorkout({ id, workoutData: body, locals });
	return json({ ...data });
}

export async function DELETE({ params, locals }) {
	const { id } = params;

	const { success, message } = await deleteWorkout({ id, locals });

	return json({ success, message });

}

