import { deleteProgram, updateProgram, getProgram } from '$lib/services/programs.services';
import { json } from '@sveltejs/kit';

export async function PATCH({ params, request, locals }) {
	const { id } = params;
	const body = await request.json();	
	const data = await updateProgram({ id, programData: body, locals });
	return json({ ...data });
}

export async function DELETE({ params, locals }) { 
	const { id } = params;
	console.log(id);
	const data = await deleteProgram({ id, locals });
	return json({ ...data });
}

export async function GET({ params, locals }) {
	const id = params.id;

	const result = await getProgram({ id, locals });

	if (!result.success) {
		return new Response(result.message || 'Program not found', { status: 404 });
	}

	return json(result);
}