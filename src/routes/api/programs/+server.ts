import { addProgram, getPrograms } from '$lib/services/programs.services.js';
import { addWorkout } from '$lib/services/workout.services';
import { generateProgramName } from '$lib/utils.js';
import { json } from '@sveltejs/kit';


export async function GET({ locals }) {
    const programs = await getPrograms(locals);
    return json(programs);
}


export async function POST({ locals }) {
    const name = generateProgramName();
    const {data, message, success} = await addProgram({name: name, locals});


    const workoutResponse = await addWorkout({ program_id: data.id, locals });

    data.workouts.push(workoutResponse.data);
  
    
    return json({data, message, success});
}