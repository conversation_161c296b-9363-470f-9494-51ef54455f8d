import { EXERCISE_API_LOGIN_URL, EXR_API_PASSWORD, EXR_API_USERNAME, PREMIUM_EXR_API_PASSWORD, PREMIUM_EXR_API_USERNAME, SS_EXR_API_USERNAME, SS_EXR_API_PASSWORD } from "$env/static/private";
import apiTokens from "$lib/stores/apiTokenStore";
import { json } from "@sveltejs/kit";

export async function GET() {
    let tokens = {premium: '', normal: '', second_search: ''};

    apiTokens.set({
        premium: await getTokens(PREMIUM_EXR_API_USERNAME, PREMIUM_EXR_API_PASSWORD),
        normal: await getTokens(EXR_API_USERNAME, EXR_API_PASSWORD),
        second_search: await getTokens(SS_EXR_API_USERNAME, SS_EXR_API_PASSWORD)
    });

    apiTokens.subscribe((value) => {
        tokens.premium = value.premium;
        tokens.normal = value.normal;
        tokens.second_search = value.second_search;
    });

    // apiTokens.set({
    //     premium: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC8yMDQuMjM1LjYwLjE5NFwvZnVzaW9cL3B1YmxpY1wvaW5kZXgucGhwIiwic3ViIjoiZThkOWQzZTgtYWY1Ny01NDg2LTk1NTctN2ZhMmI3M2ZhZDg2IiwiaWF0IjoxNzMxOTM5NjU0LCJleHAiOjE3MzE5NDMyNTQsIm5hbWUiOiJkZXZ1c2VyIn0.49hUEq72A6CUmgmRiO47G8bSe_1dNsQdzQ91s8k4ywM',
    //     normal: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC8yMDQuMjM1LjYwLjE5NFwvZnVzaW9cL3B1YmxpY1wvaW5kZXgucGhwIiwic3ViIjoiNjFjMGVmMmItYzEyZi01NTljLTgzMzctNzEzN2I1ZmEwMzY4IiwiaWF0IjoxNzMxOTM5NjU0LCJleHAiOjE3MzE5NDMyNTQsIm5hbWUiOiJkZXZ1c2VycmVzdHJpY3RlZCJ9.4HP8SuUFSKQyTGF-QpzknlXf5FU6ieyXJDpo_5UHXts'
    //   })

    return json(tokens);
}

const getTokens = async (username: string, password: string) => {
    const res = await fetch(EXERCISE_API_LOGIN_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            username: username,
            password: password
        })
    });
    const data = await res.json();
    return data.token;
}