import fs from 'node:fs';
import path from 'node:path';
import { error } from '@sveltejs/kit';
import { validateToken } from '$lib/services/token.services.js';
import { env } from '$env/dynamic/private';

const VIDEO_DIR = env.VIDEO_DIR;


function getMimeType(filename: string) {
    const ext = path.extname(filename).toLowerCase();
    switch (ext) {
        case '.mp4': return 'video/mp4';
        case '.webm': return 'video/webm';
        case '.ogg': return 'video/ogg';
        default: return 'application/octet-stream'; 
    }
}

export async function GET({ url, request, params, locals }) {
    const { id } = params;

    const token = url.searchParams.get('token');
    const filename = `${id}.mp4`;
    const filePath = path.join(VIDEO_DIR, filename);

    if (!filePath.startsWith(VIDEO_DIR)) {
        throw error(400, 'Invalid filename');
    }

    try {
        
        const data = await validateToken({ token, locals: locals });

        if (!data) {
            throw error(401, 'Unauthorized');
        }

        const stats = await fs.promises.stat(filePath);

        if (!stats.isFile()) {
            throw error(404, 'Not Found');
        }

        const fileSize = stats.size;
        const range = request.headers.get('range');
        const mimeType = getMimeType(filename);

        if (range) {
            const parts = range.replace(/bytes=/, "").split("-");
            const start = parseInt(parts[0], 10);
            const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
            const chunksize = (end - start) + 1;

             if (start >= fileSize || end >= fileSize || start > end) {
                 return new Response(null, {
                     status: 416, 
                     headers: {
                         'Content-Range': `bytes */${fileSize}` 
                     }
                 });
             }


            const fileStream = fs.createReadStream(filePath, { start, end });

            const headers = {
                'Content-Range': `bytes ${start}-${end}/${fileSize}`,
                'Accept-Ranges': 'bytes',
                'Content-Length': chunksize,
                'Content-Type': mimeType,
            };

            return new Response(fileStream, {
                status: 206, 
                headers: headers
            });

        } else {
            const headers = {
                'Content-Type': mimeType,
                'Content-Length': fileSize,
                'Accept-Ranges': 'bytes' 
            };

            const fileStream = fs.createReadStream(filePath);

            return new Response(fileStream, {
                headers: headers,
                status: 200 
            });
        }

    } catch (err: any) {
        if (err.code === 'ENOENT') {
            throw error(404, 'Video not found');
        }
        console.error('Error serving video:', err);
        throw error(500, 'Internal Server Error');
    }
}