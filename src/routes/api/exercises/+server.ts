import { json } from "@sveltejs/kit";
import { addExercise, getExercises } from '$lib/services/exercise.services';

export async function GET({ url, locals }) {
   const workout_id = url.searchParams.get('workout');

   if (!workout_id) {
      return json({ status: 'error', message: 'workout id is required' });
   }

	const exercises = await getExercises({ workout_id, locals });
	return json({ status: 'success', exercises });
}

export async function POST({ request, locals }) {
	try {
		const body = await request.json();
		const data = await addExercise({ exercise: body, locals });

		if (!data) {
			return json({ status: 'error', message: 'Failed to add exercise' });
		}

		return json({ status: 'success', ...data });
	} catch (error) {
		console.error(error);
		return json({ status: 'error', message: error.message });
	}
}
