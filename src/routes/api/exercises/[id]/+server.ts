import { json } from '@sveltejs/kit';
import { API_URL } from '$env/static/private';
import type { IAuth } from '../../../../typings';
import auth from '$lib/stores/authStore';
import { customFetch } from '$lib/fetch';
import { getExercise } from '$lib/services/exercise.services';


export async function GET({ params }) {
    const { id } = params;

    const data = await getExercise({ id });
    return json(data);

}

export async function POST({ request }) {
    try {
        const body = await request.json();

        const apiUrl = `${API_URL}/items/exercises`;

        let token;
        auth.subscribe((value: IAuth) => {
            token = value.access_token;
        });

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`
            },
            body: JSON.stringify(body)
        });

        const data = await response.json();

        if (response.status !== 200) {
            return json({ status: 'error', ...data });
        }

		return json({ status: 'success', ...data });
    } catch (error) {
        console.error(error);
        return json({ status: 'error', message: error.message });
    }

}

export async function PATCH({ params, request }) {
    try {
        const id = params.id;
		const body = await request.json();

        const apiUrl = `${API_URL}/items/exercises/${id}`;

        let token;
        auth.subscribe((value: IAuth) => {
            token = value.access_token;
        });

        const response = await fetch(apiUrl, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`
            },
            body: JSON.stringify(body)
        });

        const data = await response.json();

        if (response.status !== 200) {
            return json({ status: 'error', ...data });
        }

		return json({ status: 'success', ...data });
    } catch (error) {
        console.error(error);
        return json({ status: 'error', message: error.message });
    }
}

export async function DELETE({ params }) {
	const { id } = params;

	const res = await customFetch('/items/exercises/' + id, {
		method: 'DELETE',
	});

	return res

}
