import { json } from '@sveltejs/kit';
import { validateConcreteSession } from '$lib/services/auth.services';

export async function GET({ cookies }) {
  const sessionKey = cookies.get('CONCRETE5');
  if (!sessionKey) {
    return json({ isAuthenticated: false });
  }

  try {
    const valid = await validateConcreteSession(sessionKey);
    return json({ isAuthenticated: valid });
  } catch (e) {
    return json({ isAuthenticated: false });
  }
}
