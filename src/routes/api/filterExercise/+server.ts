import { EXERCISE_API_URL } from '$env/static/private';
import { json } from '@sveltejs/kit';
import { filterSearch } from '$lib/services/search.service.js';

export async function GET({ url, locals }) {
    const tags: string = url.searchParams.get('tags') != 'null' ? url.searchParams.get('tags') : 'null';

    const link = url.searchParams.get('link') !== 'null' ? EXERCISE_API_URL + url.searchParams.get('link') : 'null';

    try {
        const res = await filterSearch({ link, tags, locals });
        return json({
            ...res
        });
    } catch (error) {
        console.error('Error in filterSearch:', error);
        return json({
            success: false,
            message: 'Internal server error'
        }, { status: 500 });
    }
}