import { EXERCISE_API_URL } from '$env/static/private';
import apiTokens from '$lib/stores/apiTokenStore.js';
import { json } from '@sveltejs/kit';
import { cache } from '../../../config/config.js';


export async function GET({ url, locals }) {
	let isPremium = false;
	let userId = null;
	locals.userStore.subscribe((value: any) => {
		isPremium  = value.isPremium;
		userId = value?.user?.id;
	})

	let token;
	apiTokens.subscribe((value) => {
		token = isPremium ? value.premium : value.normal;
	})

	const linkParam = url.searchParams.get('link');

	const link = EXERCISE_API_URL + linkParam;

	if (!linkParam) {
		return json({
			status: 'error',
			message: 'Link is required'
		});
	}

	const cacheKey = `user-${userId}-${encodeURIComponent(linkParam)}`;
	let cacheData = null;

	if (cacheKey) {
		cacheData = cache.get(cacheKey);

		if (cacheData) {
			console.log('cache hit');
			return json({
				status: 'success',
				exercises: cacheData
			});
		}
	}

	const response = await fetch(link!, {
		method: 'GET',
		// cache: 'force-cache',
		headers: {
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${token}`
		}
	});
	
	const data = await response.json();

	
	if (response.status === 200) {

		cache.set(cacheKey, data.exercises);
		console.log('cache set');
		return json({
			status: 'success',
			exercises: data.exercises
		});
	} else {
		return json({
			status: 'error',
			...data
		});
	}
}
