import { EXERCISE_API_URL } from '$env/static/private';
import apiTokens from '$lib/stores/apiTokenStore';
import { json } from '@sveltejs/kit';
import { cache } from '../../../../config/config.js';
import { generateToken } from '$lib/services/token.services';

export async function GET({ params, locals }) {
	const { id } = params;

	let isPremium = false;
	let userId = null;
	locals.userStore.subscribe((value: any) => {
		isPremium  = value.isPremium;
		userId = value?.user?.id;
	})

	let token;
	let second_search_token;
	apiTokens.subscribe((value) => {
		token = isPremium ? value.premium : value.normal;
		second_search_token = value.second_search;
	})


	const videoToken = await generateToken({ locals: locals });

	
	const link = EXERCISE_API_URL + `/exercises?exerciseids=${id}`;
	const cacheKey = `user-${userId}-${encodeURIComponent(link)}`;
	const cacheData = cache.get(cacheKey);

	if (cacheData) {
		console.log('cache single exercise hit');
		return json({
			status: 'success',
			token: videoToken,
			exercises: cacheData
		});
	}

	const response = await fetch(link, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${token}`
		}
	});
	
	const data = await response.json();


	
	if (response.status === 200) {
		if (data.exercises.length === 0 && !isPremium) {
			// If the exercise is not found in the first search and the user is not premium, try the second search
			const secondSearchResponse = await secondSearch(id, second_search_token);
			if (secondSearchResponse.status === 'success') {
			
				cache.set(cacheKey, secondSearchResponse.exercises);
				console.log('cache single exercise set');
				return json({
					status: 'success',
					exercises: secondSearchResponse.exercises
				});
			} else {
				return json({
					status: 'error',
					...secondSearchResponse
				});
			}
		}
		// If the exercise is found in the first search, return it

		cache.set(cacheKey, data.exercises[0]);
		console.log('cache single exercise set');
		return json({
			status: 'success',
			token: videoToken,
			exercises: data.exercises[0]
		});
	} else {
		return json({
			status: 'error',
			...data
		});
	}
}

async function secondSearch(id: string, second_search_token: any) {
	const response = await fetch(`${EXERCISE_API_URL}/exercises?exerciseids=` + id, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${second_search_token}`
		}
	});
	const data = await response.json();
	if (response.status === 200) {
		return {
			status: 'success',
			exercises: data.exercises[0]
		};
	} else {
		return {
		status: 'error',
		...data
	}
	}
}