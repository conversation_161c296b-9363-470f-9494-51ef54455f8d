<script>
    import "../../app.css";
</script>

<div class="login-container min-h-screen flex bg-[#F0F2FF]">
    <div class="hidden md:flex w-2/3 bg-left-image"></div>
  
    <div class="w-full lg:w-1/2 flex items-center justify-center p-6 py-12">
      <div class="max-w-md w-full">
        <h1 class="text-2xl font-bold text-primary mb-10 max-w-[280px] lg:text-center lg:mx-auto">Hello! Register to get started</h1>
        <form method="post">
          <div class="mb-4">
            <input type="text" class="w-full p-3 border border-border rounded-md focus:outline-none focus:ring focus:ring-ring"  placeholder="First Name" id="firstName" required />
          </div>
          <div class="mb-4">
            <input type="text" class="w-full p-3 border border-border rounded-md focus:outline-none focus:ring focus:ring-ring" placeholder="Last Name" id="lastName" required />
          </div>
          <div class="mb-4">
            <input type="email" class="w-full p-3 border border-border rounded-md focus:outline-none focus:ring focus:ring-ring" placeholder="E-mail" id="email" required />
          </div>
          <div class="mb-4">
            <input type="password" class="w-full p-3 border border-border rounded-md focus:outline-none focus:ring focus:ring-ring" placeholder="Password" id="password" required />
          </div>
          <div class="mb-4">
            <input type="password" class="w-full p-3 border border-border rounded-md focus:outline-none focus:ring focus:ring-ring" placeholder="Confirm Password" id="confirm-password" required />
          </div>
          <button type="submit" class="w-full bg-[#FC570C] text-primary-foreground font-semibold p-3 rounded-md hover:bg-[#FC570C]/80">Register</button>
        </form>
        <div class="flex items-center justify-center my-8">
          <span class="text-muted-foreground font-semibold text-sm">Or Register with</span>
        </div>
        <button class="w-full bg-white text-secondary-foreground p-3 font-semibold rounded-md hover:bg-white/80 flex items-center justify-center gap-4"><img src="google.svg" alt="" class="">Continue With Google</button>
        <p class="text-center text-muted-foreground mt-8">Already have an account? <a href="/login" class="text-blue-800 font-bold">Login Now</a></p>
      </div>
    </div>

    
  </div>

  <style>
    .bg-left-image {
        background-image: url('/login-picture.png');
        background-size: cover;
        background-position: center;
    }
  
    .login-container {
      width: 100%;
      /* height: 100vh; */
    }
  </style>