<script>
    import DragDrop from '$lib/components/DragDrop.svelte'; // Adjust path if needed
    import { onMount } from 'svelte';

    export let data;
    let currentProgram = data.program;

    onMount(() => {
        currentProgram = data.program;
    });

    // Reactively update currentProgram if the data prop changes (e.g., from load function re-run)
    $: {
        if (data.program !== currentProgram) {
            currentProgram = data.program;
        }
    }

    function handleDataUpdate(event) {
        // TODO: pass info to backend to update the program
        currentProgram = event.detail.program;
    }

    function handleDeleteItem(event) {
        // TODO: pass info to backend to delete the item
        const { workoutId, exerciseId, workoutName, exerciseName } = event.detail;
        if (currentProgram && currentProgram.workouts) {
            const workoutIndex = currentProgram.workouts.findIndex((/** @type {IWorkout} */ w) => w.id === workoutId);
            if (workoutIndex !== -1 && currentProgram.workouts[workoutIndex].exercises) {
                const exerciseIndex = currentProgram.workouts[workoutIndex].exercises.findIndex((/** @type {IExercise} */ ex) => ex.workout_exercise_id === exerciseId);
                if (exerciseIndex !== -1) {
                    const updatedExercises = [...currentProgram.workouts[workoutIndex].exercises];
                    updatedExercises.splice(exerciseIndex, 1);
                    currentProgram.workouts[workoutIndex].exercises = updatedExercises;
                    currentProgram = { ...currentProgram };
                }
            }
        }
    }
</script>

<svelte:head>
    <title>Workout Drag & Drop</title>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f1f5f9; }
    </style>
</svelte:head>

<div class="page-container">
    <div class="drag-drop-wrapper">
        {#if currentProgram}
            <DragDrop
                program={currentProgram}
                on:updateData={handleDataUpdate}
                on:deleteItem={handleDeleteItem}
            />
        {:else}
            <p>Loading program data...</p>
        {/if}
    </div>
</div>

<style>
    .page-container { max-width: 700px; margin: 1rem auto; padding: 1rem; }
    .main-title { font-size: 1.875rem; font-weight: 700; margin-bottom: 1.5rem; text-align: center; color: #1e293b; }
    .drag-drop-wrapper { position: relative; }
    :global(body.dragging-workout), :global(body.dragging-exercise) { cursor: grabbing !important; }
</style>
