<script lang="ts">
	import ProgramList from "$lib/components/customs/ProgramLIst/List.svelte";
    import Button from "$lib/components/ui/button/button.svelte";
	import type { IProgram } from "$lib/typings";
	import { onMount } from "svelte";
  import { Input } from "$lib/components/ui/input";
  import { ScrollArea } from "$lib/components/ui/scroll-area/index.js";
  import moment from 'moment';
	import { Disc, PlusCircle, ThreeDotsVertical, Trash, Share, Files, ArrowRepeat, Pencil, BoxArrowInLeft } from "svelte-bootstrap-icons";
	import { toast } from "svelte-sonner";
	import { writable, type Writable } from "svelte/store";
    import * as Drawer from "$lib/components/ui/drawer";
	import { goto } from "$app/navigation";
  import { defaultProgram, selectedProgram, selectedWorkout } from "$lib/stores/generalStore";
    let { data } = $props<{ data: { programs: IProgram[] } }>();
    let programsStore = writable<IProgram[]>(data.programs);
    let isDeleteMode: boolean = $state(false);
    let isLoading: boolean = $state(false);
    let isLogoutLoading: boolean = $state(false);
    let openDrawer: boolean = $state(false);
    let mounted: boolean = $state(false);
    
    // Sorting and Searching state
    let sortCriteria: string = 'name'; 
    let sortOrder: string = 'asc'; 
    let searchQuery: string = $state(''); 
    let startDate: string = $state(''); 
    let endDate: string = $state(''); 
    let openSearchModal: boolean = $state(false);
    let dateFilterType = $state('none');

  
    onMount(() => {
      mounted = true;
        return () => {
            mounted = false;
            openDrawer = false;
            sortPrograms();
        };
    });
  
    async function handleAddProgramClick() {
        try {
            isLoading = true;
            const res = await fetch(`/api/programs`, { 
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
            });

            const responseData = await res.json();
            if (responseData.success) {
                programsStore.update(programs => [...programs, responseData.data]);
                defaultProgram.set(responseData.data);
                selectedProgram.set({value: responseData.data.id, label: responseData.data.name || responseData.data.automated_name })
                selectedWorkout.set(null); // Set selectedWorkout to null
                toast.success(responseData.message);
            } else {
                toast.error(responseData.message);
            }
        } catch (e) {
            toast.error('Failed to add program');
        } finally {
            isLoading = false;
        }
    }

    async function handleLogout() {
        isLogoutLoading = true;
        try {
            const response = await fetch('/api/auth/logout', {
                method: 'POST'
            });

            if (response.ok) {
                isLogoutLoading = false;
                toast.success('Successfully logged out');
                goto('/login');
            } else {
                toast.error('Failed to logout');
            }
        } catch (error) {
            console.error('Logout error:', error);
            toast.error('Failed to logout');
        } finally {
            isLogoutLoading = false;
        }
    }
  
    const sortPrograms = () => {
      data.programs.sort((a, b) => {
        let comparison = 0;
        
        if (sortCriteria === 'name') {
          comparison = (a.name ?? '').localeCompare(b.name ?? '');
        } else if (sortCriteria === 'automatedName') {
          comparison = a.automated_name ? a.automated_name.localeCompare(b.automated_name ? b.automated_name : '') : 1;
        } else if (sortCriteria === 'creationDate') {
          comparison = moment(a.date_created).diff(moment(b.date_created));
        }
  
        return sortOrder === 'asc' ? comparison : -comparison;
      });
      
      filterPrograms(); // Filter after sorting
    };
  
    const filterPrograms = () => {
      let filteredPrograms = data.programs;

      if (searchQuery) {
        filteredPrograms = filteredPrograms.filter(program =>
          program.name && program.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          program.automated_name && program.automated_name.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      switch (dateFilterType) {
        case 'after':
          if (startDate) {
            filteredPrograms = filteredPrograms.filter(program =>
              moment(program.date_created).isSameOrAfter(moment(startDate))
            );
          }
          break;
        case 'before':
          if (endDate) {
            filteredPrograms = filteredPrograms.filter(program =>
              moment(program.date_created).isSameOrBefore(moment(endDate))
            );
          }
          break;
        case 'between':
          if (startDate && endDate) {
            filteredPrograms = filteredPrograms.filter(program =>
              moment(program.date_created).isBetween(moment(startDate), moment(endDate), null, '[]')
            );
          }
          break;
      }
      
      programsStore.set(filteredPrograms);
  };

  
  </script>
  
  <div class="hidden sm:flex justify-between items-center p-[20px] border-b border-border ">
    
    <h1 class="text-2xl">Programs</h1>
    
    <!-- Search Button -->
    <Button class="bg-[#FC570C] text-white ml-4" on:click={() => openSearchModal = true}>
      Search & Filter
    </Button>
  </div>
  
  <!-- Search Modal -->
  {#if openSearchModal}
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-auto">
      <div class="flex justify-between items-center p-6 border-b">
        <h2 class="text-2xl font-bold text-gray-900">Programs</h2>
        <button class="text-gray-400 hover:text-gray-500" on:click={() => openSearchModal = false}>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div class="p-6 space-y-6">
        <div class="space-y-4">
          <div>
            <label for="search-input" class="block text-sm font-medium text-gray-700 mb-1">Search:</label>
            <input type="text" id="search-input" placeholder="Search by name or automated name" bind:value={searchQuery} on:input={filterPrograms} class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]" />
          </div>
          
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-1">Date Filter:</label>
              <select id="date-filter" bind:value={dateFilterType} on:change={filterPrograms} class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]">
                <option value="none">None</option>
                <option value="after">After</option>
                <option value="before">Before</option>
                <option value="between">Between</option>
              </select>
            </div>
          </div>
          
          {#if dateFilterType !== 'none'}
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {#if dateFilterType === 'after' || dateFilterType === 'between'}
                <div>
                  <label for="start-date" class="block text-sm font-medium text-gray-700 mb-1">
                    {dateFilterType === 'after' ? 'After Date:' : 'Start Date:'}
                  </label>
                  <input type="date" id="start-date" bind:value={startDate} on:change={filterPrograms} class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]" />
                </div>
              {/if}
              {#if dateFilterType === 'before' || dateFilterType === 'between'}
                <div>
                  <label for="end-date" class="block text-sm font-medium text-gray-700 mb-1">
                    {dateFilterType === 'before' ? 'Before Date:' : 'End Date:'}
                  </label>
                  <input type="date" id="end-date" bind:value={endDate} on:change={filterPrograms} class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]" />
                </div>
              {/if}
            </div>
          {/if}
          
          
          <div>
            <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort by:</label>
            <div class="flex space-x-2">
              <select id="sort" bind:value={sortCriteria} on:change={sortPrograms} class="flex-grow px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]">
                <option value="name">Sort By Name</option>
                <option value="automatedName">Sort By Automated Name</option>
                <option value="creationDate">Sort By Creation Date</option>
              </select>
              <select bind:value={sortOrder} on:change={sortPrograms} class="px-2 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]">
                <option value="asc">ASC</option>
                <option value="desc">DEC</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      
      <div class="px-6 py-4 bg-gray-50 border-t flex justify-end">
        <button class="px-4 py-2 bg-[#FC570C] text-white rounded-md hover:bg-[#e54d0b] focus:outline-none focus:ring-2 focus:ring-[#FC570C] focus:ring-offset-2" on:click={() => { filterPrograms(); openSearchModal = false; }}>
          Done
        </button>
      </div>
    </div>
  </div>
  {/if}
  
  <Button 
  class="flex bg-[#FC570C] focus:bg-[#FC570C] active:bg-[#FC570C] text-white fixed bottom-[90px] sm:bottom-2 right-2 z-40"
  disabled={isLoading} 
  on:click={handleAddProgramClick}
>
  {#if isLoading}
    <Disc class="mr-2 animate-spin"/>
    Adding
  {:else}
    <PlusCircle class="mr-2"/>
    Add Program          
  {/if}
  </Button>
  
  <div class="relative overflow-hidden">
    <div class="sm:hidden border-b border-gray-200 flex items-center justify-between py-2 px-4"> 
        <h1 class="text-xl text-gray-500 font-bold mb-0">{'Programs'}</h1>
        <div class="sm:hidden">
            <div class="flex flex-row items-center">
                <Button variant="ghost" class="text-black p-0" on:click={() => openDrawer = !openDrawer}>
                    <ThreeDotsVertical class="size-6" />
                </Button>
            </div>
        </div>
    </div>
   
    {#if !$programsStore || $programsStore.length === 0}
        <div class="flex-1 py-10 px-4 md:px-0 flex items-center justify-center text-gray-500">
            No programs found. Click the Add Program button to create one.
        </div>
    {/if}
    <div class="h-[calc(100dvh-130px)] sm:h-[calc(100dvh-120px)] overflow-y-auto">
        <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5 gap-1">
            <ProgramList store={programsStore} isDeleteMode={isDeleteMode} />
        </div>
    </div>
</div>


<Drawer.Root open={openDrawer} onOpenChange={(val: boolean) => { openDrawer = val }}>
    <Drawer.Content class="flex flex-col gap-2 p-4 max-w-[600px] w-full mx-auto sm:top-0 sm:bottom-[100px]">
        <Button 
            variant="ghost" 
            class="flex gap-4 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
            on:click={() => {
                isDeleteMode = !isDeleteMode;
                openDrawer = false;
            }}
        >
            {#if isDeleteMode}
                <svg xmlns="http://www.w3.org/2000/svg" class="size-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
                Exit Delete Mode
            {:else}
                <Trash class="size-6" />
                Enter Delete Mode
            {/if}
        </Button>

        <Button 
            variant="ghost" 
            class="flex gap-4 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
            on:click={() => {
                openSearchModal = true;
                openDrawer = false;
            }}
        >
        <svg xmlns="http://www.w3.org/2000/svg" class="size-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
      </svg>
            Search & Filter Programs
        </Button>

        <Button 
            variant="ghost" 
            class="flex gap-4 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
            on:click={() => {
                openDrawer = false;
            }}
        >
            <Share class="size-6" />
            Share Program
        </Button>

        <!-- <Button 
            variant="ghost" 
            class="flex gap-4 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
            on:click={() => {
                openDrawer = false;
            }}
        >
            <Files class="size-6" />
            Duplicate Program
        </Button> -->

        <Button 
            variant="ghost" 
            class="flex gap-4 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
            on:click={() => {
                openDrawer = false;
            }}
        >
            <ArrowRepeat class="size-6" />
            Create Template from Program
        </Button>

        <Button 
            variant="ghost" 
            class="flex gap-4 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
            on:click={() => {
                openDrawer = false;
            }}
        >
            <Pencil class="size-6" />
            Edit Program Details
        </Button>
        <Button 
        variant="ghost" 
        class="flex gap-4 bg-[#F0F2FF] rounded justify-start items-center text-[16px]"
        on:click={() => {
            handleLogout();
           if(!isLogoutLoading){
            openDrawer = false;
           }
        }}
        disabled={isLogoutLoading}
    >
            {#if isLogoutLoading}
                <Disc class="mr-2 animate-spin"/> 
            {:else}
                <BoxArrowInLeft class="size-6" />
            {/if}
            Logout
        </Button>
    </Drawer.Content>
</Drawer.Root>