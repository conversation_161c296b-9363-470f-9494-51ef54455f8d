import { getProgram, getPrograms } from '$lib/services/programs.services';
import { getUserTags } from '$lib/services/tag.service';
import { error } from '@sveltejs/kit';


export const load = async ({ params, locals }) => {
	const id = params.id;
	const { data, success } = await getProgram({ id, locals });
	const uData: any = {};
	locals.userStore.subscribe((userStore: any) => {
		uData.user = userStore?.user;
		uData.isPremium = userStore?.isPremium;
		uData.isAuthenticated = userStore?.isAuthenticated;
		uData.status = userStore?.status;
		uData.email = userStore?.email;
	});

	const { data: tags, success: tagsSuccess } = await getUserTags({ locals });

	if (!success) {
		throw error(404, 'Program not found');
	}


	if (!tagsSuccess) {
		throw error(404, 'Tags not found');
	}

	const programs = await getPrograms(locals);



	return {
		program: data,
		programs: programs.data,
		tags,
		uData
	};
};