<script lang="ts">
  import Board from "$lib/components/customs/Board/Board.svelte";
    import DragDrop from "$lib/components/DragDrop.svelte"; 
  import * as Tabs from "$lib/components/ui/tabs/index.js";
  import DesignPage from "$lib/components/customs/DesignPage/DesignPage.svelte";
  import { tick } from "svelte";

  import Header from "$lib/components/customs/Header/Header.svelte";
  import { goto, invalidate } from "$app/navigation";
  import { Disc, ArrowRepeat, Pencil, Clipboard2Plus, Plus, PlusCircle, Trash, ChevronBarUp, ChevronBarDown, BoxArrowInLeft, Search, QuestionCircle, Share, Journals } from "svelte-bootstrap-icons";
  import { writeClipboard } from "$lib/utils";
  import ExerciseDetailsPage from "$lib/components/customs/ExerciseDetailsPage/ExerciseDetailsPage.svelte";
  import { exerciseDetails, showExerciseDetailsPage, showSettingsPage, allThePrograms } from "$lib/stores/generalStore";
  import * as Drawer from "$lib/components/ui/drawer";
  import * as Dialog from "$lib/components/ui/dialog";
  import { Input } from "$lib/components/ui/input";
  import { Button } from "$lib/components/ui/button/index.js";
  import { toast } from "svelte-sonner";
  import SearchBrowse from "$lib/components/customs/SearchBrowse/searchbrowse.svelte";
  import { Hamburger } from 'svelte-hamburgers';
  import ProgramList from "$lib/components/customs/ProgramLIst/List.svelte";

  import type { IProgram, IWorkout, IExercise, IAddExerciseToWorkout, DefaultTab } from "$lib/typings";
  import { updateKanban, deleteExerciseWorkout } from "$lib/utils/workouts_exercises.utils";
  import { onMount } from "svelte";
  import { writable } from "svelte/store";
  import { get } from "svelte/store";
  import { showDesignPage, defaultTab, newProgramDetails, filter_is_open } from "$lib/stores/generalStore";
  import { page } from "$app/stores";

  import { defaultProgram, selectedProgram, selectedWorkout, deleteProgramMode, isProgramTab } from "$lib/stores/generalStore";

  import { helpsUrl } from '$lib/stores/generalStore';
  import { isBoardScrolled } from '$lib/stores/generalStore'; // Import the store
  // import { $isBoardScrolled } from 'svelte/store'; // Use the store value
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { ThreeDotsVertical, XLg, Arrow90degLeft, Arrow90degRight } from 'svelte-bootstrap-icons';
	import Settings from "$lib/components/customs/settings/Settings.svelte";
	import { resolveTransformedExercises } from "$lib/utils/tags_exercise.utils.js";


  const { data } = $props();

  let programsStore = writable<IProgram[]>(data.programs);
  let program: IProgram = $state(data.program);

  

  let openBottomDrawer: boolean = $state(false)
  let isLoading = $state(false);
  let isDeleteMode = $state(false);
  let isDesktop = $state(false);
  let isLogoutLoading: boolean = $state(false);
  let isMobile: boolean = $state(false);
  let isTablet: boolean = $state(false);
  let programName = $state(program.name);
  let programAutoName = $state(program.automated_name);
  let open = $state(true);



  const workoutsStore = writable<IWorkout[]>(program.workouts);





  let isProgramFilterOpen: boolean = $state(false);

  let name = $state(program.name);
  let openEditProgramDialog = $state(false);

  let isTop: boolean = $state(false);

  let isFilterOpen: boolean = $state(false);
  let isSearchOpen: boolean = $state(true);
	let results: any[] = $state([]);
	let searchValue = $state('');


  let originTab: string = $state('programs');

  // Browse-container related variables
  let path: string[] = $state([]);
  let currentData: any = $state([]);
  let exercises: any[] = $state([]);
  // let deleteProgramMode: boolean = $state(false);

  const tagStore = writable<any>([]);
  tagStore.set(data.tags);

  const programId = $page.params.id;


  async function loadWorkouts() {
    const temp = []
    for (let i = 0; i < program.workouts.length; i++) {
      const w = program.workouts[i];
      temp.push({
        ...w,
        exercises: await resolveTransformedExercises(w.exercises)
      })
    }
    workoutsStore.set(temp);
  }

onMount(() => {  
    $inspect(data.program); 
    originTab = 'exercises';

    loadWorkouts();
    defaultProgram.set(data.program); // Set the default program
    allThePrograms.set(data.programs); // Set all programs
    selectedProgram.set({value: data.program.id, label: data.program.name || data.program.automated_name });
    console.log('isBoardScrolled', isBoardScrolled);
    

    
    const checkScreenSize = () => {
      isDesktop = window.innerWidth >= 1024;
      isMobile = window.innerWidth < 768;	  
      isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;
    };

    const checkDefaultTab = () => {
      defaultTab.set(isDesktop ? "exercises" : "programs");
    };


    checkScreenSize();
    checkDefaultTab();
    window.addEventListener('resize', () => {
      checkScreenSize();
      checkDefaultTab(); // Re-check default tab on resize as well
    });
    
    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
})

$effect(() => {
  name = data.program.name;
  programAutoName = data.program.automated_name;
  const program = data.program;
  if (program) {


    workoutsStore.set(program.workouts);
  }
});

function formatProgramName(startMonth: string, startYear: number, endMonth: string, endYear: number, alphabet: string) {
        if (startYear === endYear) {
            return startMonth === endMonth
                ? `${startMonth} ${startYear} ${alphabet || ''}`
                : `${startMonth} ${endMonth} ${startYear || ''}`;
        } else {
            return `${startMonth} ${startYear} / ${endMonth} ${endYear} ${alphabet || ''}`;
    }
}

function renameProgram() {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.toLocaleString('en-us', { month: 'short' });

  // Parse the existing automated name
  const nameParts = program.automated_name.split(' ');


  let startMonth: string = "";
  let startYear: number = 0;
  let alphabet: string = "";

  if (Number.isFinite(+nameParts[0])) {
      startMonth = nameParts[1];
      startYear = parseInt(nameParts[0]);
      alphabet = nameParts[2] || '';
  } else if (Number.isFinite(+nameParts[1])) {
      startYear = parseInt(nameParts[1]);
      startMonth = nameParts[0];
      alphabet = nameParts[5] || '';
  } else {
      startYear = parseInt(nameParts[2]);
      startMonth = nameParts[0];
      alphabet = nameParts[3] || '';
  }

  // Extract the end date
  const newProgramName = formatProgramName(startMonth, startYear, month, year, alphabet);

    fetch('/api/programs/'+ program.id, {
          method: 'PATCH',
          headers: {
              'Content-Type': 'application/json'
          },
          body: JSON.stringify({
              automated_name: newProgramName
          })
      })
}
async function handleLogout() {
        isLogoutLoading = true;
        try {
            const response = await fetch('/api/auth/logout', {
                method: 'POST'
            });

            if (response.ok) {
                isLogoutLoading = false;
                toast.success('Successfully logged out');
                goto('/login');
            } else {
                toast.error('Failed to logout');
            }
        } catch (error) {
            console.error('Logout error:', error);
            toast.error('Failed to logout');
        } finally {
            isLogoutLoading = false;
        }
    }

const handleAddWorkout = async () => {
  isLoading = true;
  const response = await fetch('/api/workouts', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({program_id: data.program.id, last: data.program.last})
  });

  const workout = await response.json();
  
  data.program.last = workout.next

  selectedWorkout.set({value: workout.data.id, label: workout.data.name || workout.data.automated_name })
  selectedProgram.set({value: data.program.id, label: data.program.name || data.program.automated_name })

  workoutsStore.set([ ...$workoutsStore, workout.data ]);
 
  toast.success(workout.message);
  renameProgram();

  openBottomDrawer = false;
  isLoading = false;
}

const handleEditProgramClick = async () => {
    
    const res = await fetch('/api/programs/'+ $page.params.id, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: program.name
        })
    })

    const data = await res.json();

    if (!data.success) {
        toast.error(data.message);
        return;
    }

    newProgramDetails.set({
        value: $page.params.id,
        label: program.name 
    });

    programsStore.update(programs => {
        const programIndex = programs.findIndex(p => p.id === data.data.id);
        if (programIndex !== -1) {
            programs[programIndex].name = data.data.name;
        }
        return programs;
    });


    openEditProgramDialog = false;
    
    toast.success(data.message);

};


export function handleTabChange(tab: DefaultTab) {
    defaultTab.set(tab);
  }

  function handleExerciseTabClick() {
    showSettingsPage.set(false);
    isFilterOpen = false; 
    isSearchOpen = true;
    results = [];
    searchValue = '';

    originTab = 'exercises'; 
    path = []; 
    currentData = [];
  }

  function changeTabValue(value: DefaultTab) {
    defaultTab.set(value);

    switch (value) {
      case "programs":
        helpsUrl.set('https://exrx.net/WorkoutWebApp/Programs');
        break;
      case "exercises":
        helpsUrl.set('https://exrx.net/WorkoutWebApp/Exercises');
        break;  
      default:
          helpsUrl.set('https://exrx.net/WorkoutWebApp');
        break;
    }
  }
  function sendToHelpSection() {
    const url = get(helpsUrl);

    // Check if the URL is external
    if (url) {
      window.open(url, '_blank'); // Open external URLs in a new tab
    } else {
	  window.open('https://exrx.net/WorkoutWebApp', '_blank');
    }
  }

      async function handleCopyProgram() {
        const programToCopy = data.program;
        const copied = await writeClipboard(programToCopy);

        if(copied) {
            toast.success('Program copied to clipboard!');
        } else {
            toast.error('Failed to copy program. Please try again.');
        }
    }

  function showBackIcon() {
    if (isProgramFilterOpen || isFilterOpen) {
      return true;
    }

    return ['programs', 'exercises'].includes($defaultTab!) ? false : true;
  }


  async function handleAddProgramClick() {
        try {
            isLoading = true;
            const res = await fetch(`/api/programs`, { 
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
            });

            const responseData = await res.json();
            if (responseData.success) {
                programsStore.update(programs => [...programs, responseData.data]);
                defaultProgram.set(responseData.data);
                selectedProgram.set({value: responseData.data.id, label: responseData.data.name || responseData.data.automated_name })
                selectedWorkout.set(null); // Set selectedWorkout to null
                toast.success(responseData.message);
                openBottomDrawer = false;
            } else {
                toast.error(responseData.message);
            }
        } catch (e) {
            toast.error('Failed to add program');
        } finally {
            isLoading = false;
        }
    }
</script>



<Header 
  backIcon = {showBackIcon()}
  onBackIconClick={() => { goto('/programs'), isProgramFilterOpen = false, isFilterOpen = false }}
  title={programAutoName}
  subtitle={name}
  originPage = {originTab}
  threeDotsIcon={open}
  onThreeDotsIconClick={() => openBottomDrawer = !openBottomDrawer}
  onArrowLeftRightClick={() => {
    showDesignPage.set(!$showDesignPage);
    defaultTab.set('exercises');
  }}
/>

<div class="flex flex-col lg:flex-row md:flex-row gap-1 h-[calc(100dvh-120px)] sm:h-[calc(100dvh-64px)] overflow-hidden">
  <div class="">
    <div class="flex justify-between w-full">
		<div class="inline-flex">
			<div class={isMobile || isTablet ? "hidden ml-2" : "ml-2 mt-1"}  style="width: 48px; height: 20">
				<Hamburger bind:open --layer-width="20px" --layer-height="1px"/>
			</div>
		</div>

    <div class="flex-1 flex justify-center items-center -mt-1">
      {#if (!isMobile && open)}
        <h1 class="text-[21px] text-[#677489] font-semibold">{programAutoName}</h1>
      {/if}
    </div>

    {#if (!isMobile && open)}
		<div>
			<DropdownMenu.Root>
				<DropdownMenu.Trigger>
					<ThreeDotsVertical class={$defaultTab === "programs" ? "hidden md:block size-6 text-primary mt-4 mr-5" : `hidden md:block size-6 text-primary mt-4 mr-1.5`} />
				</DropdownMenu.Trigger>
				{#if !isMobile}
				<DropdownMenu.Content>
        <DropdownMenu.Group
				  class="flex flex-col gap-2 p-2 max-w-[600px] mx-auto sm:top-0">
          {#if $defaultTab === "programs"}
				<DropdownMenu.Item 
				class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
				on:click={() => {
				openEditProgramDialog = true;
				openBottomDrawer = false;
				}}
			>
				<Pencil class="size-5" />
        <span class="flex-1">Edit Program Name</span>
			</DropdownMenu.Item>
      <DropdownMenu.Item
				class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
        disabled={isLoading}
				on:click={() => {
				deleteProgramMode.set(!get(deleteProgramMode));
        // console.log('deleteProgramMode', get(deleteProgramMode));
				}}
			>
				<Trash class="size-5" />
        <span class="flex-1"> {get(deleteProgramMode) == true ? 'Cancel Delete mode' : 'Delete Program Mode'}</span>
        <!-- <span class="flex-1">Delete Program Mode</span> -->
           

			</DropdownMenu.Item>
      <DropdownMenu.Item
				class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
        disabled={isLoading}
        on:click={handleAddProgramClick}
			>
				<Clipboard2Plus class="size-5" />
        <span class="flex-1">Add Program</span>
			</DropdownMenu.Item>
      <!-- <DropdownMenu.Item
				class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
        disabled={isLoading}>	
      	<Trash class="size-5" />
        <span class="flex-1">Copy Program with Links</span>
			</DropdownMenu.Item> -->
			<!-- <DropdownMenu.Item
				class="flex gap-3 {isLoading
				? 'text-[#313da3]'
				: ''} bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
				on:click={handleAddWorkout}
			>
				{#if isLoading}
				<Disc class="size-5 animate-spin" />
				Adding
				{:else}
				<Clipboard2Plus class="size-5" />
        <span class="flex-1">Add Workout</span>
				{/if}
			</DropdownMenu.Item> -->
      {/if}
      <DropdownMenu.Item
         class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
        on:click={handleCopyProgram}
        >
        <Journals class="size-6" />
        <span class="flex-1">Copy Programs with Links</span>
			</DropdownMenu.Item>
			<DropdownMenu.Item
			class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
			on:click={() => {
				sendToHelpSection();
			}}
			>
			<QuestionCircle class="size-5" />
      <span class="flex-1">Help</span>
			</DropdownMenu.Item>
      
			<DropdownMenu.Item
			class="flex gap-3 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
			on:click={() => {
				handleLogout();
				if (!isLogoutLoading) {
				openBottomDrawer = false;
				}
			}}
			>
			{#if isLogoutLoading}
				<Disc class="mr-2 size-5 animate-spin" />
			{:else}
				<BoxArrowInLeft class="size-5" />
			{/if}
      <span class="flex-1">Logout</span>
			</DropdownMenu.Item>
        </DropdownMenu.Group>
        </DropdownMenu.Content>
				{/if}
		</DropdownMenu.Root>
	</div>
    {/if}

	</div>
  {#if open}
  <div class="{isTablet ? 'w-[384px]' : 'w-full'} lg:w-[428px] lg:min-w-[428px] overflow-y-auto flex flex-col lg:ml-4">
    <Tabs.Root value={$defaultTab} onValueChange={(value) => changeTabValue(value)} class="w-full flex flex-col flex-1">
    <!-- <Tabs.Root value={defaultTab} onValueChange={(value) => defaultTab = value} class="w-full flex flex-col flex-1"> -->
      <Tabs.List class="flex w-full">
        <Tabs.Trigger 
          value="programs" 
          class="w-1/2 text-lg"
          on:click={() => {
            originTab = 'programs';
            isProgramFilterOpen = false;
            showSettingsPage.set(false);
            isProgramTab.set(true);
          }}
          >Programs
        </Tabs.Trigger>
        <Tabs.Trigger
          value="exercises"
          class="w-1/2 text-lg"
          on:click={() => {handleExerciseTabClick(); isProgramTab.set(false);}}
        >
          Exercises
        </Tabs.Trigger>
      </Tabs.List>
      <div class="flex-1 overflow-hidden">
        <Tabs.Content value="programs" class="h-full overflow-y-auto">
          <ProgramList 
            store={programsStore} 
            isDeleteMode={isDeleteMode} 
            bind:isProgramFilterOpen={isProgramFilterOpen}
            changeTab={() => {
              console.log('changed')
              if (isMobile) {
                showSettingsPage.set(false);
                isFilterOpen = false; 
                originTab = 'exercises'; 
                path = []; 
                currentData = [];
                isSearchOpen = true;
                showDesignPage.set(true)
              }
              
              changeTabValue('exercises')
            }}
            />
        </Tabs.Content>
        <Tabs.Content value="exercises" class="overflow-y-auto">
          <div class="h-[calc(100dvh-220px)] sm:h-[calc(100dvh-190px)]">
            <SearchBrowse
                programsStore={programsStore}
                workoutsStore={workoutsStore}
                isPremium={data.uData?.isPremium}
                tagsStore={tagStore}
                bind:isFilterOpen={isFilterOpen}
                bind:isSearchOpen={isSearchOpen}
                bind:results={results}
                bind:searchValue={searchValue}
                bind:path={path}
                bind:currentData={currentData}
                bind:exercises={exercises}
            />
          </div>
        </Tabs.Content>
      </div>
    </Tabs.Root>
  </div>
  {/if}
</div>
<div class="flex-grow lg:block overflow-hidden">
  {#if $showDesignPage && isMobile}
    <div class="absolute inset-0 z-50 bg-[#F0F2FF] overflow-y-auto">
      <DesignPage workoutsStore={workoutsStore} data={data} isDeleteMode={isDeleteMode} tagStore={tagStore}/>
    </div>
  {:else if $showExerciseDetailsPage}
    <ExerciseDetailsPage workoutStore={workoutsStore} tagStore={tagStore} isPremium={data.uData?.isPremium} programs={data.programs} />
  {:else if isTablet}
    <DesignPage workoutsStore={workoutsStore} data={data} isDeleteMode={isDeleteMode} tagStore={tagStore}/>
  {:else if $showSettingsPage}
    <Settings uData={data.uData}/>
  {:else}
    <div class:hidden={!$showDesignPage || isTablet || isMobile} class="flex flex-col h-full mt-2">
      <Board 
      workoutsStore={workoutsStore} 
      programId={data.program.id} 
      {isDeleteMode} 
      programName={data.program.name} 
      programAutoName={data.program.automated_name} 
      program={data.program} 
    />
    </div>
  {/if}
  {#if $defaultTab !== "exercises" || !showExerciseDetailsPage}
        <!-- <Button class="flex bg-[#FC570C] focus:bg-[#FC570C] active:bg-[#FC570C] text-white fixed bottom-[90px] sm:bottom-2 right-2 z-40 {$isBoardScrolled ? 'rounded-full z-50 w-12 h-12 p-0 justify-center' : ''} {isProgramFilterOpen ? "hidden" : ""}" on:click={() => handleTabChange("exercises")}>
          {#if $isBoardScrolled} 
          <PlusCircle class="size-4" />  
          {:else}
          Add Exercise
          {/if}
        </Button> -->
  {/if}
</div>
</div>

{#if $showExerciseDetailsPage && !isDesktop}
  <div class="fixed inset-0 z-50 bg-[#F0F2FF] overflow-y-auto">
    <ExerciseDetailsPage workoutStore={workoutsStore} tagStore={tagStore} isPremium={data.uData?.isPremium} programs={data.programs}/>
  </div>
{/if}

{#if $showSettingsPage && !isDesktop}
  <div class="fixed inset-0 z-50 bg-[#F0F2FF] overflow-y-auto">
    <Settings uData={data.uData}/>
  </div>
{/if}

<Drawer.Root open={openBottomDrawer} onOpenChange={(val: boolean) => { openBottomDrawer = val }}>
<Drawer.Content class="flex flex-col gap-2 p-4 max-w-[850px] w-full mx-auto sm:bottom-[50px]">
  {#if $defaultTab === "programs"}
  <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" on:click={ () => { 
      openEditProgramDialog = true; openBottomDrawer = false 
    } }>
    <Pencil class="size-6" />
    Edit Program Name
  </Button>
  <!-- <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" on:click={ () => {} }>
    <Share class="size-6" />
    Share Program
  </Button> -->
  <!-- <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" on:click={ () => {} }>
    <Share class="size-6" />
    Copy Program with Links
  </Button> -->
    <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]"
    disabled={isLoading}
    on:click={handleAddProgramClick}
    >
		<Clipboard2Plus class="size-5" />
    Add Program
  </Button>

  <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" on:click={() => {
    deleteProgramMode.set(!get(deleteProgramMode));
    openBottomDrawer = false;
  }}>
    <Trash class="size-6" />
    {get(deleteProgramMode) ? 'Cancel Delete' : 'Delete Program Mode'}
  </Button>
  {/if}
  <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" 
    on:click={ () => {
        sendToHelpSection();
      } 
    }>
    <QuestionCircle class="size-6" />
    Help
  </Button>
  <Button 
  variant="ghost" 
  class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]"
  on:click={() => {
      handleLogout();
     if(!isLogoutLoading){
      openBottomDrawer = false;
     }
  }}
  disabled={isLogoutLoading}
>
      {#if isLogoutLoading}
          <Disc class="mr-2 animate-spin"/> 
      {:else}
          <BoxArrowInLeft class="size-6" />
      {/if}
      Logout
  </Button>
</Drawer.Content>
</Drawer.Root>

<Dialog.Root  open={openEditProgramDialog} onOpenChange={(val) => { openEditProgramDialog = val }} >
<Dialog.Content class="rounded-[2rem] top-[230px] ">
  <Dialog.Header>
    <Dialog.Title>Edit program name</Dialog.Title>
  </Dialog.Header>
  <input type="hidden" name="id" value={program.id} />
  <div class="mt-4">
      <Input type="text" name="name" bind:value={name} on:blur={(e: any) => {program.name = e.target.value}} placeholder="program name" required 
        class="max-w-full border-white border-b-gray-600 focus:border-white focus:outline-white focus:ring-0 focus:ring-offset-0 focus:ring-offset-gray-800 focus:ring-white" />
  </div>
  <div class="flex justify-between items-center mt-4 mx-[5rem]">
    <Button variant="ghost" type="button" class="text-[#FC570C] font-bold text-sm" on:click={() => { openEditProgramDialog = false; program.name = name }}>Cancel</Button>
    <Button variant="ghost" type="button" class="text-[#FC570C] font-bold text-sm" on:click={handleEditProgramClick}>Save</Button>
  </div>
</Dialog.Content>
</Dialog.Root>