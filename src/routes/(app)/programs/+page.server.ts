import { getPrograms } from '$lib/services/programs.services';
import type { IProgram } from '$lib/typings';
import { redirect } from '@sveltejs/kit';


export const load = async ({ locals, fetch }) => {
	const { data, success } = await getPrograms(locals);
	if (success && data.length != 0) {
		throw redirect(303, `/programs/${data[0].id}`);
	} else if (success && data.length == 0) {
		const newProgram = await createNewProgramCall(fetch);
		throw redirect(303, `/programs/${newProgram.id}`);
	}
};


async function createNewProgramCall (fetch): Promise<IProgram> {
	try {
		const res = await fetch('/api/programs', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
		});
		const data = await res.json();
		if (!res.ok) {
			throw new Error(`Failed to create new program. Status: ${res.status}`);
		}
		return data.data;
	} catch (error: any) {
		console.error("Error in createNewProgramCall:", error);
		throw new Error(error.message || 'An unexpected error occurred while creating the program.');
	}
}