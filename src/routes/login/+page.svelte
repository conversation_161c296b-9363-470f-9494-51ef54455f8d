<script lang="ts">
  import "../../app.css";
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { Toaster, toast } from 'svelte-sonner';
  import { enhance } from '$app/forms';
  import type { ActionResult, req } from '@sveltejs/kit';
  import { Disc, Eye, EyeSlash } from "svelte-bootstrap-icons";
  let error = '';
  let success = false;
  let isSubmitting = $state(false);

  let showPassword: boolean = $state(false);

  // export let form;
  let { form, concreteUrl, workoutWebAppUrl } = $props<{ data: { form: any, concreteUrl: string, workoutWebAppUrl: string } }>();

  // export let data;
  
  const CONCRETE_URL = concreteUrl;
	const WORKOUT_WEB_APP_URL = workoutWebAppUrl;

  onMount(() => {
    if (form?.error) {
      error = form.error;
      toast.error("Invalid Credentials");
    }
    
    if (form?.success) {
      success = form.success;
      toast.success("Welcome Back. Successfully Signed In");
      setTimeout(() => {
        goto('/programs');
      }, 1000);
    }
  });

  function handleEnhance() {
    isSubmitting = true;
    return async ({ result }: { result: ActionResult }) => {
      if (result.type === 'failure') {
        error = result.data?.error;
        toast.error("Invalid Credentials");
        isSubmitting = false;
      }
      if (result.type === 'success') {
        success = true;
        toast.success("Welcome Back. Successfully Signed In");
        setTimeout(() => {
          goto('/programs');
        }, 1000);
      }
    };
  }
</script>

<svelte:head>
  <title>Login - ExRx.net Workout Web App</title>
</svelte:head>

<div class="login-container min-h-screen flex bg-[#F0F2FF]">
  <div class="hidden md:flex w-2/3 bg-left-image"></div>

  <div class="w-full lg:w-1/2 flex items-center justify-center p-6 py-8">
    <div class="max-w-md w-full">
      <img src="/exrx-icon.gif" alt="ExRx.net Logo" class="w-[200px] h-16 object-contain mx-auto" />
      <h1 class="text-2xl font-bold text-primary mb-10 max-w-[280px] text-center mx-auto">Welcome back! Glad to see you, Again!</h1>
      <Toaster position="top-right" richColors/>
      <form method="POST" use:enhance={handleEnhance}>
        <div class="mb-4">
          <input type="email" name="email" class="w-full p-3 border border-border rounded-md focus:outline-none focus:ring focus:ring-ring" value="" placeholder="Email" id="email" required />
        </div>
        <div class="mb-4 relative">
          <input type={showPassword ? 'text' : 'password'} name="password" class="w-full p-3 pr-8 border border-border rounded-md focus:outline-none focus:ring focus:ring-ring" value="" placeholder="Password" id="password" required />
          <button 
            type="button" class="absolute right-2 top-4"
            onclick={() => {
              showPassword = !showPassword;
            }}  
          >
            {#if showPassword}
              <Eye class="w-5 h-5 text-muted-foreground" />
            {:else}
              <EyeSlash class="w-5 h-5 text-muted-foreground" />
            {/if}
          </button>
        </div>
        <div class="flex justify-end mb-6">
          <a href={`${CONCRETE_URL}/login/concrete/forgot_password?redirect_url=${WORKOUT_WEB_APP_URL}`} class="text-muted-foreground text-sm font-semibold">
            Forgot Password?
          </a>
        </div>
        <button type="submit" disabled={isSubmitting} class="w-full bg-[#FC570C] text-primary-foreground font-semibold p-3 rounded-md hover:bg-[#FC570C]/80 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2">
          {#if isSubmitting}
            <Disc class="w-5 h-5 animate-spin text-white" />
            <span class="text-white">Logging in...</span>
          {:else}
            Login
          {/if}
        </button>
      </form>

      <!-- <div class="relative my-8">
        <div class="absolute inset-0 flex items-center">
          <span class="w-full border-t border-border"></span>
        </div>
        <div class="relative flex justify-center text-xs uppercase">
          <span class="bg-[#F0F2FF] px-2 text-muted-foreground">Or sign in with</span>
        </div>
      </div> -->

      <p class="text-center text-muted-foreground mt-8">Don't have an account? <a href={`${CONCRETE_URL}/api/register?redirect_url=${WORKOUT_WEB_APP_URL}`} class="text-blue-800 font-bold">Register Now</a></p>
    </div>
  </div>
</div>

<style>
  .bg-left-image {
    background-image: url('/login-picture.png');
    background-size: cover;
    background-position: center;
  }

  .login-container {
    width: 100%;
    height: 100vh;
  }
</style>
