import { CONCRETE_URL, SENTRY_DSN } from "$env/static/private";
import { directusCustomAuth } from "$lib/services/auth.services";
import { addUser, getUserByEmail } from "$lib/services/user.services";
import { getUserStore } from "$lib/stores/store";
import type { IAuth } from "$lib/typings";
import { redirect } from "@sveltejs/kit";
import * as Sentry from "@sentry/sveltekit";


Sentry.init({
  dsn: SENTRY_DSN,
  tracesSampleRate: 1.0,
});


export const handle = async ({ event, resolve }) => {
  const sessionId = event.cookies.get('CONCRETE5');  

  // Create a user store based on the session ID
  const userStore = sessionId ? getUserStore(sessionId) : null;
  let userData: IAuth | null = null;

  if (userStore) {
    userStore.subscribe((value: IAuth) => {
      userData = value;
    });
  }


  const isLoggedIn = !!userData;

  const pathname = event.url.pathname;

  // Prevent access to /login and /register if already logged in
  if (isLoggedIn && (pathname === '/login' || pathname === '/register')) {
    throw redirect(302, '/programs');
  }

  // Redirect from landing page based on auth status
  if (pathname === '/') {
    if (isLoggedIn) {
      throw redirect(302, '/programs');
    } else {
      throw redirect(302, '/login');
    }
  }

  // We can also add /register if we want the user to be able to register without redirecting to concrete
  if (!isLoggedIn && (pathname === '/login' || pathname === '/api/tokens')) {
    return await resolve(event);
  }

  // If there's no session cookie, force login
  if (!sessionId) {
    throw redirect(302, '/login');
  }

  try {
    // If user is already in store, attach and continue
    if (userData) {
      event.locals.sessionId = sessionId;
      event.locals.userStore = userStore;
      return await resolve(event);
    }

    // Validate session with Concrete CMS
    const concrete = await validateSession(sessionId);
    if (!concrete || !concrete.success) {
      return new Response('Redirect', { status: 303, headers: { Location: '/login' } });
    }


    // Check Directus for existing user
    let user = await getUserByEmail({ email: concrete.email });
    if (!user) {
      user = await addUser({ email: concrete.email });
    }

    // Authenticate with Directus
    const directus = await directusCustomAuth({ email: concrete.email });

    // Save auth data to store
    const authData: IAuth = {
      user: concrete.user,
      email: concrete.email,
      isPremium: concrete.isPremium,
      status: concrete.status,
      refresh_token: directus.refresh_token,
      access_token: directus.access_token,
      isAuthenticated: true,
    };

    userStore?.set(authData);

    event.locals.sessionId = sessionId;
    event.locals.userStore = userStore;

    return await resolve(event);

  } catch (err) {
    console.error(err);
    throw err;
  }
};


const myErrorHandler = ({ error, event }) => {
  console.error("An error occurred on the server side:", error, event);
};

export const handleError = Sentry.handleErrorWithSentry(myErrorHandler);

const validateSession = async (sessionId: string | undefined) => {
	if (!sessionId) {
		return null;
	}
	try {
    const response = await fetch(`${CONCRETE_URL}/api/session/validate/${sessionId}`);
    
    return await response.json();
  } catch (err) {
    return null
  }
}