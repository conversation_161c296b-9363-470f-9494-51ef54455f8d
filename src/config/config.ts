import { env } from "$env/dynamic/private";
import { LRUCache } from "lru-cache";


export const cache = new LRUCache({
	ttl: parseInt(env.CACHE_TTL),
    maxSize: parseInt(env.CACHE_MAXSIZE),
    sizeCalculation: (value, key) => {
        try {
            const jsonString = JSON.stringify(value);
            return Buffer.byteLength(jsonString, 'utf8');
       } catch (e) {
            console.error("Failed to calculate size for caching:", e);
            return 0;
       }
      },
	allowStale: false,
    ttlAutopurge: true,
})