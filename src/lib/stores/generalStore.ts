import { writable } from 'svelte/store';
import { type DefaultTab, type IProgram, type IWorkout } from "$lib/typings";


const defaultProgram = writable<any>(null);
const selectedWorkout = writable<{ value: string | null, label: string | null } | null>(null);
const selectedProgram = writable<{ value: string | null, label: string | null } | null>(null);

// const defaultProgram = writable< { value: string, label: string } | null >(null);

const showExerciseDetailsPage = writable<boolean> (false)

const showAddExerciseMenuItem = writable<boolean>(false);

const showMoveExerciseMenuItem = writable<boolean>(false);

const showDesignPage = writable<boolean>(true);

const exerciseDetails = writable<any> (null)

const videoToken = writable<any>(null);

const draggedNode = writable<any> (null)

const timer = writable<any>(null);

const isHover = writable<boolean>(false);

const cookiesStore = writable<any>(null);

const helpsUrl = writable('https://exrx.net/WorkoutTools/Exercises');

const isBoardScrolled = writable<boolean>(false);

const showSettingsPage = writable<boolean>(false);

const defaultTab = writable<DefaultTab>('exercises');
const current_workout_exercise_id = writable<string | null>(null);
const current_workout_id = writable<string | null>(null);


const filter_is_open= writable<boolean>(false);



type ActionType =
  | null
  | 'addWorkout'
  | 'editWorkout'
  | 'duplicateProgram'
  | 'copyProgramLink'
  | 'toggleDeleteMode'
  | 'help'
  | 'logout';
const newProgramDetails = writable<{ value: string | null | undefined, label: string | null } | null>(null);

const workoutsUpdateStore = writable<WorkoutsStore>({
    workouts: [],
    lastUpdated: Date.now()
});

interface IProgram {
        id: string,
        name: string,
        automated_name: string,
        workouts?: any[],
    }

export interface WorkoutsStore {
    workouts: IWorkout[];
    lastUpdated: number;
}

const allThePrograms = writable<IProgram[]>([]);

const deleteProgramMode = writable<boolean>(false);

const isProgramTab = writable<boolean>(false);

export const layoutAction = writable<ActionType>(null);


export {
    cookiesStore,
    defaultProgram,
    selectedWorkout,
    showExerciseDetailsPage,
    showAddExerciseMenuItem,
    showMoveExerciseMenuItem,
    showDesignPage,
    exerciseDetails,
    showSettingsPage,
    draggedNode,
    isHover,
    timer,
    selectedProgram,
    helpsUrl,
    isBoardScrolled,
    videoToken,
    defaultTab,
    allThePrograms,
    deleteProgramMode,
    current_workout_exercise_id,
    current_workout_id,
    newProgramDetails,
    workoutsUpdateStore,
    isProgramTab,
    filter_is_open,
}

