import type { Writable } from 'svelte/store';

export interface IUser {
	uid: number | null;
	cid: number;
	username: string;
}

export interface IUserStore {
	sessionId: string;
	userStore: Writable<any>
}

export interface IAuth {
	user:any;
	isPremium: boolean;
	email: string;
	refresh_token: string | null;
	access_token: string | null;
	isAuthenticated: boolean;
	status: string;
}


export interface IResponse {
	success: boolean;
	message?: string;
	data?: any;
}

// export interface IExercise {
// 	id: string;
// 	name: string;
// 	img: string;
// 	icon: string;
// 	cat: string;
// }

export interface IAddExerciseToWorkout {
	exercise: IExerciseBackend;
	workout_id: string;
	position: number;
	id?: string;
}

export interface IWorkoutExercise {
	id?: number;
  	workout_id: number;
  	exercise_id: string;
  	position: number;
}


export interface IProgram {
	id: string;
	name: string;
	automated_name: string;
	last: string;
	date_created: string;
	date_updated: string;
	workouts: IWorkout[];
}

export interface IWorkout {
	id: string;
	name: string;
	automated_name: string;
	position: string;
	program_id: string;
	date_created: string;
	exercises: IExercise[];
}

export interface IExercise {
	id: number;
	name: string;
	img: string;
	icon: string;
	cat: string;
	color?: string[];
	invisible?: boolean;
	exercise_id: string;
	url?: string;
}

export interface IExerciseBackend {
	id: string;
	name: string;
	img: string;
	icon: string;
	cat: string;
	color?: string[];
	invisible?: boolean;
	workout_exercise_id: number;
	url?: string;
	workout_exercise_id?: number;
}


export interface IFetchRequestBody {
	method?: string;
	headers?: Record<string, string>;
	body?: string;
} 

export interface ICustomFetchParams {
	url: string,
	options?: IFetchRequestBody
	locals?: App.Locals
}

export interface IDNDEvent {
	type: 'consider' | 'finalize',
	detail: {
		info: { id: number, source: string, trigger: string },
		items: any[]
	}
}
export type DefaultTab = 'programs' | 'exercises';

export interface ExerciseData {
	Exercise_Id: string;
	Exercise_Name: string;
	Exercise_Name_Complete_Abbreviation?: string;
	Overall_Category: string;
	Apparatus_Groups_Name: string;
	Apparatus_Abbreviation: string;
	Utility_Name: string;
	Utility_Icon: string;
	Movement_Name: string;
	Force: string;
	Duration_Time: string;
	Duration_Reps: string;
	Duration_Interval: string;
	Cardio_RPE1_10: string;
	Larg_Img_1: string;
	Small_Img_1: string;
	Instructions_Preparation: string;
	Instructions_Execution: string;
	video_src?: string;
	[key: string]: any;
}