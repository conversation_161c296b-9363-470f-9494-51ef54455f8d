import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { cubicOut } from "svelte/easing";
import type { TransitionConfig } from "svelte/transition";
import type { IProgram } from "$lib/typings";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

type FlyAndScaleParams = {
	y?: number;
	x?: number;
	start?: number;
	duration?: number;
};

export function longpress(node) {
    const TIME_MS = 2000;
    const MOUSE_TIME_MS = 0;
    let timeoutPtr;
    let isTouchEvent = false;
    let initialY;
    let isGrabbed = false;
    let isDragging = false;
    let initialTouchPos = { x: 0, y: 0 };
    let mouseDownTime;

    function handleStart(e) {
        isTouchEvent = e.type === 'touchstart';
        
        if (isTouchEvent) {
            initialY = e.touches[0].clientY;
            initialTouchPos = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
            window.addEventListener('touchmove', handleMoveBeforeLong, { passive: true });
            
            timeoutPtr = window.setTimeout(() => {
                isGrabbed = true;
                window.removeEventListener('touchmove', handleMoveBeforeLong);
                window.addEventListener('touchmove', handleMoveAfterLong, { passive: false });
                node.dispatchEvent(new CustomEvent('long'));
                window.setTimeout(() => node.dispatchEvent(e), 0);
            }, TIME_MS);
        } else {
            mouseDownTime = Date.now();
            timeoutPtr = window.setTimeout(() => {
                isGrabbed = true;
                node.dispatchEvent(new CustomEvent('long'));
                window.setTimeout(() => node.dispatchEvent(e), 0);
            }, MOUSE_TIME_MS);
        }
    }

    function handleMoveBeforeLong(e) {
        if (isTouchEvent && !isGrabbed) {
            const currentY = e.touches[0].clientY;
            if (Math.abs(currentY - initialY) > 5) {
                window.clearTimeout(timeoutPtr);
                window.removeEventListener('touchmove', handleMoveBeforeLong);
            }
        }
    }

    function handleMoveAfterLong(e) {
        if (!isDragging) {
            const touch = e.touches[0];
            const deltaX = Math.abs(touch.clientX - initialTouchPos.x);
            const deltaY = Math.abs(touch.clientY - initialTouchPos.y);
            
            if (deltaX > 5 || deltaY > 5) {
                isDragging = true;
            }
        }

        if (isDragging) {
            e.preventDefault();
        }
    }

    function handleEnd(e) {
        if (isTouchEvent) {
            window.clearTimeout(timeoutPtr);
            window.removeEventListener('touchmove', handleMoveBeforeLong);
            window.removeEventListener('touchmove', handleMoveAfterLong);
            isGrabbed = false;
            isDragging = true;
        } else {
            window.clearTimeout(timeoutPtr);
            if (isGrabbed) {
                node.dispatchEvent(new CustomEvent('long')); // Trigger release
            }
            isGrabbed = false;
        }
    }

    node.style.userSelect = 'none';
    node.style.webkitUserSelect = 'none';
    node.style.msUserSelect = 'none';
    node.style.webkitTapHighlightColor = 'transparent';

    node.addEventListener('mousedown', handleStart, { passive: false });
    node.addEventListener('mouseup', handleEnd);
    node.addEventListener('mouseleave', handleEnd);
    node.addEventListener('touchstart', handleStart, { passive: true });
    node.addEventListener('touchend', handleEnd);

    return {
        destroy: () => {
            // node.removeEventListener('mousedown', handleStart);
            // node.removeEventListener('mouseup', handleEnd);
            // node.removeEventListener('mouseleave', handleEnd);
            // node.removeEventListener('touchstart', handleStart);
            // node.removeEventListener('touchend', handleEnd);
            // window.removeEventListener('touchmove', handleMoveBeforeLong);
            // window.removeEventListener('touchmove', handleMoveAfterLong);
            
            node.style.userSelect = '';
            node.style.webkitUserSelect = '';
            node.style.msUserSelect = '';
            node.style.webkitTapHighlightColor = '';
        }
    };
}


export function longpressWorkoutItem(node) {
    const TIME_MS = 500;
    let timeoutPtr;
    let isTouchEvent = false;
    let mouseDownTime;
    let isGrabbed = false;

    function handleStart(e) {
        isTouchEvent = e.type === 'touchstart';

        
        // Start the long press timer
        timeoutPtr = window.setTimeout(() => {
            node.dispatchEvent(new CustomEvent('long')); // Trigger the long press event
        }, TIME_MS);

        
    }

    function handleEnd() {
        window.clearTimeout(timeoutPtr); // Clear the timer when the user ends the interaction
        window.removeEventListener('touchmove',()=>{});
    }

    // Attach event listeners to detect long press
    node.addEventListener('mousedown', handleStart);
    node.addEventListener('mouseup', handleEnd);
    node.addEventListener('mouseleave', handleEnd);
    node.addEventListener('touchstart', handleStart, { passive: true });
    
    node.addEventListener('touchend', handleEnd);

    return {
        // destroy: () => {
        //     // Cleanup event listeners
        //     node.removeEventListener('mousedown', handleStart);
        //     node.removeEventListener('mouseup', handleEnd);
        //     node.removeEventListener('mouseleave', handleEnd);
        //     node.removeEventListener('touchstart', handleStart);
        //     node.removeEventListener('touchend', handleEnd);
        // }
    };
}

export const flyAndScale = (
	node: Element,
	params: FlyAndScaleParams = { y: -8, x: 0, start: 0.95, duration: 150 }
): TransitionConfig => {
	const style = getComputedStyle(node);
	const transform = style.transform === "none" ? "" : style.transform;

	const scaleConversion = (
		valueA: number,
		scaleA: [number, number],
		scaleB: [number, number]
	) => {
		const [minA, maxA] = scaleA;
		const [minB, maxB] = scaleB;

		const percentage = (valueA - minA) / (maxA - minA);
		const valueB = percentage * (maxB - minB) + minB;

		return valueB;
	};

	const styleToString = (
		style: Record<string, number | string | undefined>
	): string => {
		return Object.keys(style).reduce((str, key) => {
			if (style[key] === undefined) return str;
			return str + `${key}:${style[key]};`;
		}, "");
	};

	return {
		duration: params.duration ?? 200,
		delay: 0,
		css: (t) => {
			const y = scaleConversion(t, [0, 1], [params.y ?? 5, 0]);
			const x = scaleConversion(t, [0, 1], [params.x ?? 0, 0]);
			const scale = scaleConversion(t, [0, 1], [params.start ?? 0.95, 1]);

			return styleToString({
				transform: `${transform} translate3d(${x}px, ${y}px, 0) scale(${scale})`,
				opacity: t
			});
		},
		easing: cubicOut
	};
};

export const generateProgramName = () => {
	const currentDate = new Date();
	const year = currentDate.getFullYear();
	const month = currentDate.toLocaleString('en-us', {  month: 'short' });

	return `${month} ${year}`;
}

export function getNextLetter(letter: string) {
	if (!letter) return 'A';
	const nextCharCode = letter.charCodeAt(0) + 1;
	return String.fromCharCode(nextCharCode);
}

export function getPreviousLetter(letter: string) {
	if (!letter) return 'A';
	const prevCharCode = letter.charCodeAt(0) - 1;
	return String.fromCharCode(prevCharCode);
}


export function buildProgramString(program: IProgram, buildForOldBrowser: boolean = false): { text: string, html: string } {
	if (buildForOldBrowser) {
		let text = `${program.name ?? program.automated_name}\n\n`;
		let html = `<h1>${program.name ?? program.automated_name}</h1>`;

		program.workouts.forEach((workout, workoutIndex) => {
			text += `${workout.name ?? workout.automated_name}\n\n`;
			html += `<h2>${workout.name ?? workout.automated_name}</h2>`;

			workout.exercises.forEach((exercise) => {
				text += `${exercise.name}`;
				if (exercise.url) {
					text += ` (${exercise.url})`;
				}
				text += '\n';
				html += `<p><a href="${exercise.url ?? '#'}">${exercise.name}</a></p>`;
			});

			// Add newline between workouts
			if (workoutIndex < program.workouts.length - 1) {
				text += '\n';
				html += '<br>';
			}
		});

		return { text, html };
	}

	// Build the header with program name
	let markdown = `# ${program.name}\n\n`;
	let html = `<h1>${program.name ?? program.automated_name}</h1>`;

	// Add each workout
	program.workouts.forEach((workout, workoutIndex) => {
		markdown += `## ${workout.name ?? workout.automated_name}\n\n`;
		html += `<h2>${workout.name ?? workout.automated_name}</h2>`;

		// Add exercises for each workout
		workout.exercises.forEach((exercise) => {
			markdown += `[${exercise.name}](${exercise.url ?? '#'})\n\n`;
			html += `<p><a href="${exercise.url ?? '#'}">${exercise.name}</a></p>`;
		});

		// Add newline between workouts
		if (workoutIndex < program.workouts.length - 1) {
			markdown += '\n';
			html += '<br>';
		}
	});

	return { text: markdown, html };
}

export async function readClipboard(): Promise<boolean | DOMException> {
    try {
        if( !navigator.clipboard || !navigator.clipboard.read) {
            throw new DOMException();
        }

        const items = await navigator.clipboard.read();
        const htmlItems = items.filter(item => item.types.includes('text/html'));
        if (htmlItems.length === 0) {
            console.error('No HTML content found in clipboard');
            return false;
        }
        return htmlItems.length > 0;

    } catch ({name, message}: any) {
        if (name === 'NotAllowedError') {
            return new DOMException();
        } else {
            console.error('Clipboard read failed:', message);
            return false;
        }
    }
}

export async function writeClipboard(program: IProgram): Promise<boolean> {
    try {
        const { html, text } = buildProgramString(program, false);
        const htmlBlob = new Blob([html], { type: 'text/html' });
        const textBlob = new Blob([text], { type: 'text/plain' });
        const clipboardItem = new ClipboardItem({
            'text/html': htmlBlob,
            'text/plain': textBlob
        });
        await navigator.clipboard.write([clipboardItem]);
        const hasHtml = await readClipboard();
        if(hasHtml instanceof DOMException) {
            throw new DOMException();
        } else if (!hasHtml) {
            console.error('No HTML content found in clipboard');
            return false;
        }
        console.log("using navigator.clipboard.write at top-level");
        return true;
    } catch (error: any) {
        if (error instanceof DOMException) {
            console.log("using copyWithLegacyMethod here");
            return copyWithLegacyMethod(program);
        }
        const { text } = buildProgramString(program, true);
        try {
            const textBlob = new Blob([text], { type: 'text/plain' });
            const clipboardItem = new ClipboardItem({
                'text/plain': textBlob
            });
            await navigator.clipboard.write([clipboardItem]);
            console.log("using navigator.clipboard.write here");
            return true;
        } catch ({name, message}: any) {
            return copyWithLegacyMethod(program);
        }
    }
}

export function copyWithLegacyMethod(program: IProgram): boolean {
	try {
		const { text: plainText, html } = buildProgramString(program, true);
		
		// Create the copy event listener
		function listener(e: ClipboardEvent) {
			e.clipboardData?.setData("text/html", html);
			e.clipboardData?.setData("text/plain", plainText);
			e.preventDefault();
		}

		// Add listener, execute copy command, then clean up
		document.addEventListener("copy", listener);
		document.execCommand("copy");
		document.removeEventListener("copy", listener);
        console.log("using execCommand with copy");
		return true;
	} catch (e) {
		console.error('Legacy clipboard method failed:', e);
		return false;
	}
}

export function extractVideoId(url: string) {


    const regex = /\/external\/(\d+)\.m3u8/;

    const match = url.match(regex);

    let videoId = null;

    if (match && match[1]) {
        videoId = match[1];
    }

    return videoId;
}
