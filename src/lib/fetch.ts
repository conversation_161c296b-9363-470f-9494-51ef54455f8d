import { API_URL } from "$env/static/private";
import type { ICustomFetchParams } from "./typings";


export async function customFetch({
    url, locals, options = {}
}: ICustomFetchParams) {

    url = `${API_URL}${url}`;

    let token;
    locals!.userStore.subscribe((value) => {
        token = value.access_token;
    });

	// Add the Authorization header to the request
	options.headers = {
		...options.headers,
		'Content-Type': 'application/json',
		Authorization: `Bearer ${token}`
	};



	let response = await fetch(url, {cache: 'no-store', ...options});

	if (response.status === 401) {
		const newToken = await refreshToken(locals!);

		if (newToken) {
			options.headers['Authorization'] = `Bearer ${newToken}`;
			response = await fetch(url, options);
		}
	}

	return response;
}

export async function refreshToken(locals: App.Locals) {
    console.log('***Refresh Token***');
    
    let refreshToken;
    locals!.userStore.subscribe((value) => {
        refreshToken = value.refresh_token
    });

    console.log(refreshToken);


	const response = await fetch(`${API_URL}/auth/refresh`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			refresh_token: refreshToken,
			mode: 'json'
		})
	});

    const data = await response.json();

    if(response.status === 401){
        return null;
    }

    locals!.userStore.update((value) => ({
        ...value,
        access_token: data.data.access_token,
        refresh_token: data.data.refresh_token,
    }))
    
    return data.data.access_token;
}
