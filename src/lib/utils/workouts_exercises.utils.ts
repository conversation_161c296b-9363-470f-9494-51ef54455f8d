import type { IAddExerciseToWorkout } from "$lib/typings";


export const addExerciseToWorkout = async (body : IAddExerciseToWorkout) => {

    const res = await fetch('/api/workouts_exercises', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json'},
        body: JSON.stringify(body),
    })

    const data = await res.json();

}



export const updateKanban = async (items : IAddExerciseToWorkout[]) => {


    if (!items.length) return;


     items = items.filter(Boolean);


    const body = items.map((item: IAddExerciseToWorkout) => {
        return {
            ...item,
            exercise: {
                ...item.exercise,
                id: item.exercise.exercise_id,
                workout_exercise_id: item.exercise.data_id
            },
           
        }
    });
    
    const res = await fetch('/api/workouts_exercises', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json'},
        body: JSON.stringify({items: body}),
    });

    const data = await res.json();

    return data;

}

export const deleteExerciseWorkout = async (id: number | null | undefined) => {
    try {
    console.log('id in delete exercise', id);
    
    // if (id === null || id === undefined) return;

    const res = await fetch("/api/workouts_exercises", {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json'},
        body: JSON.stringify({ id }),
    });

    const data = await res.json();
    console.log('data in delete exercise', data);
        return data.success;
        
    } catch (error) {
        return console.error('Error deleting exercise from workout:exercise', error);
    }
};