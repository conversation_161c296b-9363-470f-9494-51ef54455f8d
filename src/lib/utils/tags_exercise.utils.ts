export const transformExerciseItem = async (items: any[]) => {
    const updatedExercises: any[] = [];
    
    for (const item of items) {
        const exercises = await getExerciseTags(item.exercise_id);
        
        if (exercises && Array.isArray(exercises) && exercises.length > 0) {
            const colors = exercises.map(exercise => exercise.tags_id?.color).filter(color => color !== undefined);
            
            updatedExercises.push({
                ...item,
                color: colors
            });
        } else {
            updatedExercises.push({
                ...item,
                color: []
            });
        }
    }
    return updatedExercises;
};

const getExerciseTags = async (id: number) => {
    const res = await fetch(`/api/tags?exerciseId=${id}`, {
        method: 'GET'
    });
    const data = await res.json();
    if (data.success && data.data) {
      return data.data;
    }
  };

  export const resolveTransformedExercises = async (items: any[]): Promise<any[]> => {
    try {
        const transformedData = await transformExerciseItem(items);
        return transformedData;
    } catch (error) {
        console.error('Error resolving transformed exercises:', error);
        return items.map(item => ({ ...item, color: [] }));
    }
};