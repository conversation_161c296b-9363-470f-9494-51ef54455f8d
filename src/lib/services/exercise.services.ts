import { customFetch } from "$lib/fetch";
import type { IExerciseBackend } from "$lib/typings";


export const getExercises = async ({ workout_id, locals }: { workout_id: string, locals: App.Locals }) => {
	const res = await customFetch({
		locals,
		url: `/items/exercises?filter[workout_id][_eq]=${workout_id}&sort[]=position`
	});
	const data = await res.json();
	return data.data;;
};


export const getExercise = async ({ id, fields="*", locals } : { id: string, fields?: string, locals: App.Locals }) => {
	const res = await customFetch({
		locals,
		url: `/items/exercise/${id}?fields[]=${fields}`
	});
	const data = await res.json();

	if (res.status === 200) {
		return data.data
	}
    return null
}

export const addExercise = async ({ 
	exercise,
	locals
} : {
	exercise: IExerciseBackend,
	locals: App.Locals
}) : Promise<null | { id: string, name: string }> => {
	const body = JSON.stringify(exercise);

	const res = await customFetch({
		url: '/items/exercise', 
		locals,
		options: {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: body
		}
	});


	const data = await res.json();

	if (res.status === 200) {
		return data.data
	}

    return null;
}

// export const updateExercise = async ({
// 	id,
// 	name,
// 	workoutId
// }: {
// 	id: string;
// 	name: string;
// 	workoutId: string;
// }) => {
// 	const apiUrl = `${API_URL}/items/workout_exercises/${id}`;
// 	const body = JSON.stringify({ name, workoutId });
// 	const response = await fetch(apiUrl, {
// 		method: 'PUT',
// 		headers: {
// 			'Content-Type': 'application/json'
// 		},
// 		body: body
// 	});

// 	const data = await response.json();

// 	if (response.status !== 200) {
// 		return data;
// 	}
// 	console.log('data', data);

// 	return data;
// };
