import { customFetch } from '$lib/fetch';
import { getNextLetter, getPreviousLetter } from '$lib/utils';
import { getProgram, updateProgram } from './programs.services';
import type { IResponse, IUserStore, IWorkout } from '$lib/typings';


export const getWorkouts = async ({ programId, locals } : { programId: string, locals: IUserStore }) : Promise<IResponse> => {
    const res = await customFetch({
        url: `/items/workouts?filter[program_id][_eq]=${programId}&sort[]=position&fields[]=*,exercises.*,exercises.exercise_id.*`,
        locals
    });
    const data = await res.json();
    
    if (res.status === 200) {
        return {
            success: true,
            data: data.data
        }
    }

    return {
        success: false,
        message: 'Failed to get workouts'
    }
}

export const updateWorkouts = async ({ items, locals } : { items: { id: string, automated_name: string, position: string, program_id: string}[], locals: App.Locals}) => {
    try {
        let lastLetter = '';
        let program_id = '';
        for (const item of items) {
            await updateWorkout({
                id: item.id,
                workoutData: {
                    automated_name: item.automated_name,
                    position: item.position
                },
                locals
            });

            lastLetter = item.position;
            program_id = item.program_id;
        }

            await updateProgram({
                id: program_id,
                programData: {last: lastLetter},
                locals
            });


    } catch (err) {
        console.log(err)
        throw err;
    }
}

export const addWorkout = async (
    { program_id, locals } : { program_id: string, locals: IUserStore }
) : Promise<IResponse> => {


    const program = await getProgram({ id: program_id, fields: 'id,last', locals})
    const nextLetter = getNextLetter(program.data.last);
    const automated_name = `Workout ${nextLetter}`;


    const res = await customFetch({
        url: '/items/workouts', 
        locals,
        options: {
            method: 'POST',
            body: JSON.stringify({ automated_name, program_id, position: nextLetter})
        }
    });

    await updateProgram({
        id: program_id,
        programData: {last: nextLetter},
        locals
    });

    const data = await res.json();

    if (res.status === 200) {
        return {
            success: true,
            message: 'Workout added successfully',
            data: data.data
        };
    }

    return {
        success: false,
        message: 'Failed to add a new workout'
    }
}

export const addWorkoutBefore = async (
    { program_id, before,  locals } : { program_id: string, before: string, locals: IUserStore }
) : Promise<IResponse> => {
    const nextLetter = getPreviousLetter(before);
    const automated_name = `Workout ${nextLetter}`;


    const res = await customFetch({
        url: '/items/workouts', 
        locals,
        options: {
            method: 'POST',
            body: JSON.stringify({ automated_name, program_id, position: nextLetter})
        }
    });


    const data = await res.json();

    if (res.status === 200) {
        return {
            success: true,
            message: 'Workout added successfully',
            data: data.data
        };
    }

    return {
        success: false,
        message: 'Failed to add a new workout'
    }
}

export const addWorkAfter = async (
    { program_id, after,  locals } : { program_id: string, after: string, locals: IUserStore }
) : Promise<IResponse> => {
    const nextLetter = getNextLetter(after);
    const automated_name = `Workout ${nextLetter}`;


    const res = await customFetch({
        url: '/items/workouts', 
        locals,
        options: {
            method: 'POST',
            body: JSON.stringify({ automated_name, program_id, position: nextLetter})
        }
    });

    const data = await res.json();

    if (res.status === 200) {
        return {
            success: true,
            message: 'Workout added successfully',
            data: data.data
        };
    }

    return {
        success: false,
        message: 'Failed to add a new workout'
    }
}

export const updateExerciseToAnotherWorkout = async ({ workout_exercise_id, workout_id, exercise_id, position, locals } : 
    { 
        workout_exercise_id: string, 
        workout_id: string, 
        exercise_id: string, 
        position: string,
        locals: App.Locals
     }) => {
    const res = await customFetch({
        url: `/items/workouts_exercises/${workout_exercise_id}`, 
        locals,
        options: {
            method: 'PATCH',
            body: JSON.stringify({ workout_id, exercise_id, position })
        }
    });

    const data = await res.json();

    // console.log(data)

    if (res.status === 200) {
        return { success: true, data };
    }

    return { success: false, message: 'Failed to update exercise to another workout' };
}

export const addExerciseToWorkout = async ({ workout_id, exercise_id, position, locals } : 
    { workout_id: string, exercise_id: string, position: string, locals: App.Locals }) => {

    const res = await customFetch({
        url: `/items/workouts_exercises`, 
        locals,
        options: {
            method: 'POST',
            body: JSON.stringify({ workout_id, exercise_id, position })
        }
    });

    const data = await res.json();

    if (res.status === 200) {
        return {
            status: true,
            data: data.data,
            message: "Exercise added to workout"
        }
    }

    if (res.status === 400) {
        return {
            status: true,
            data: null,
            message: "This Exercise already exists in the workout"
        }
    }

    return {
        status: false,
        message: "Something went wrong"
    }

}

export const updateWorkout = async (
    { id, workoutData, locals } : {
        id: string, 
        locals: App.Locals,
        workoutData: Partial<IWorkout>
    }) : Promise<IResponse> => {
     const res = await customFetch({
        url: `/items/workouts/${id}`, 
        locals,
        options:{
            method: 'PATCH',
            body: JSON.stringify(workoutData)
        }
     });

    const data = await res.json();

    if (res.status === 200) {
        return {
            success: true,
            data: data.data
        }
    }
    return {
        success: false,
        message: 'Failed to update workout'
    }
}

export const deleteWorkout = async({ id, locals } : { id: string, locals: App.Locals }) : Promise<IResponse> => {    
    const res = await customFetch({
        url: `/items/workouts/${id}`, 
        locals,
        options: {
            method: 'DELETE',
        }
    });

    if (res.status === 204) {
        return {
            success: true,
            message: 'Workout deleted successufully'
        }
    }

    return { success: false, message: 'An error occur while deleting the workout'}
}
