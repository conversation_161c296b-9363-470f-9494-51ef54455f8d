import { customFetch } from "$lib/fetch";


export const generateToken = async ({ locals } : { locals: App.Locals }) => {

    const tokenString = generateRandomToken(32);

    const body = JSON.stringify({
        token: tokenString,
    });

    try {
        await customFetch({
            url: '/items/video_token', 
            locals,
            options: {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: body
            }
        });

        return tokenString;
    } catch (error) {
        console.error(error);
    }

};

export const validateToken = async ({ token, locals } : { token: string | null, locals: App.Locals }) => {
    try {
        const res = await customFetch({
		    locals,
            url: `/items/video_token?filter={ "token": { "_eq": "${token}" } }`,
        });

        if (res.status !== 200) {
            return false
        }

        return true
    } catch (error) {
        console.error(error);
        return false
    }
}

const generateRandomToken = (length: number) => {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
}