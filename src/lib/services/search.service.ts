import { customFetch } from '$lib/fetch';
import apiTokens from '$lib/stores/apiTokenStore';


export const filterSearch = async ({ link, tags, locals }: { link: string | null; tags: string | null, locals: App.Locals }) => {
    try {
        let isPremium = false;
        locals.userStore.subscribe((value) => {
            isPremium  = value.isPremium;
        })
    
        let token;
        apiTokens.subscribe((value) => {
            token = isPremium ? value.premium : value.normal;
        })
        
        // check if link exists & tag is null
        if (link != 'null' && tags == 'null') {
            const res = await fetch(`${link}`,{
                method: 'GET',
                cache: 'force-cache',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });
            const data = await res.json();


            return {
                success: true,
                data: data.exercises,
            };
        } else if (tags != 'null' && link == 'null') {
            // check if tags exists & link is null
            const exercises = [];
            const res = await customFetch({
                url: `/items/tags/${parseInt(tags!)}?fields[]=*,exercises.exercise_id.*`,
                locals,
            });

            const data = await res.json();
            if (data.data.exercises) {
                for (const exercise of data.data.exercises) {
                    // console.log("exercise", exercise);
                    exercises.push(exercise.exercise_id);
                }
            }
            return {
                success: true,
                data: exercises,
            }
        } else if (link != 'null' && tags != 'null') {
            // check if both fields exists

            const exercise_res = await fetch(`${link}`, {
                method: 'GET',
                cache: 'force-cache',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });
            const exercise_data = await exercise_res.json();

            const tag_res = await customFetch({
                url: `/items/tags/${parseInt(tags!)}?fields[]=*,exercises.exercise_id.*`,
                locals,
            })
            
            const tag_data = await tag_res.json();

            const exercises = [];
            for (const exercise of exercise_data.exercises) {
                const matchingTaggedExercise = tag_data.data.exercises.find((v:any) =>
                    v.exercise_id.api_id === exercise.Exercise_Id
                );

                if (matchingTaggedExercise) {
                    exercises.push(exercise);
                }
            }

            return {
                success: true,
                data: exercises,
            }
        } else if (link == 'null' && tags == 'null') {
            // check if both fields doesn't exists
            return {
                success: false,
                message: 'Please provide either tag or a filter to search'
            }
        }
    } catch (error) {
        console.log(error)
        return {
            success: false,
            message: "An error occured",
            errors: [],
        };
    }
};