import { customFetch } from '$lib/fetch';
import type { IProgram, IResponse, IUserStore } from '$lib/typings';


export const getPrograms = async (locals: IUserStore): Promise<IResponse> => {

	const res = await customFetch({
		url: `/items/programs?sort[]=name&fields[]=*`,
		locals,
	});

	if (res.status === 200) {
		const data = await res.json();

		return {
			success: true,
			data: data.data
		}
	}

	return {
		success: false,
		message: 'Failed to get programs'
	}
};


export const getNextProgramName = async ({ name, locals }: { name: string, locals: IUserStore }): Promise<string | null> => {

	const res = await customFetch({
		url: `/items/programs?filter={ "automated_name": { "_istarts_with": "${name}" }}&limit=1&sort=-date_created&fields=automated_name`,
		locals
	})
	const data = await res.json();

	let newSuffix = '';
	
	if (res.status === 200) {
		console.log('**', data.data.length);
		
		if (data.data.length > 0) {
			const lastName =  data.data[0].automated_name;

			let lastLetter = lastName.slice(-1);

			if (lastLetter == ' ') lastLetter = 'A';

			newSuffix = String.fromCharCode(lastLetter.charCodeAt(0) + 1); 

			return `${name} ${newSuffix}`;
		}

	}
	return `${name} ${newSuffix}`;
}

export const getProgram = async (
	{ id, locals, fields = '*,workouts.*,workouts.exercises.id,workouts.exercises.exercise_id.*' } : 
	{ id: string; fields?: string, locals: IUserStore}
) : Promise<IResponse> => {
	const res = await customFetch(
		{ url: `/items/programs/${id}?fields[]=${fields}`, locals }
	);
	const data = await res.json();
	let transformedData = data.data;

	if (data.data?.workouts) {
		transformedData = {
			...data.data,
			workouts: data.data.workouts.map(workout => ({
			...workout,
			exercises: workout.exercises.map(exercise => ({
				// workout_exercise_id: exercise.id,
				...exercise.exercise_id,  // Spread properties from `exercise_id` directly into `exercises`
				exercise_id: exercise.exercise_id.id,
				id: exercise.id,
				data_id: exercise.id
			}))
			}))
		};
	}

	if (res.status === 200) {
		return {
			success: true,
			data: transformedData
		}
	}

    return {
        success: false,
        message: 'Failed to get program'
    }

};

export const addProgram = async ({ name, locals }: { name:  string, locals: IUserStore }) : Promise<IResponse> => {

	const newAutomatedName = await getNextProgramName({ name, locals })

	if (!newAutomatedName) {
		return {
			success: false,
			message: 'Failed to create program'
		}
	}
	
	const res = await customFetch({
		url: '/items/programs', 
		locals,
		options: {
			method: 'POST',
			body: JSON.stringify({ automated_name: newAutomatedName })
		}
	});

	const data = await res.json();

	if (res.status === 200) {
		return {
			success: true,
			message: 'Program added successfully',
			data: data.data
		}
	}

    return {
        success: false,
        message: 'Failed to add a new program'
    }
};

export const updateProgram = async (
	{ id, programData, locals } : 
	{ 
		locals: IUserStore;
		id: string; 
		programData: Partial<IProgram> 
	}
) : Promise<IResponse> => {
    const res = await customFetch({
		url: `/items/programs/${id}`, 
		locals,
		options: {
			method: 'PATCH',
			body: JSON.stringify(programData)
		}
	});
	const data = await res.json();

	if (res.status === 200) {
		return {
			success: true,
			message: 'Program updated successfully',
			data: data.data
		}
	}
    return {
        success: false,
        message: 'Failed to update program'
    }
};

export const deleteProgram = async ({id, locals}: { id: string, locals: App.Locals }) => {
	const res = await customFetch({
		locals,
		url: `/items/programs/${id}`,
		options: {
			method: 'DELETE'
		}
	});

	
    if (res.status === 204) {
		return {
			success: true,
			message: 'Program deleted successfully'
		}
	}
	
	if (res.status === 404) {
		return {
			success: false,
			message: 'Program not found'
		}
	}

    return {
        success: true,
        message: 'Program deleted'
    }

};
