import { API_TOKEN, API_URL, CONCRETE_URL } from '$env/static/private';
import { redirect } from '@sveltejs/kit';

export const login = async ({ email, password }: { email: FormDataEntryValue; password: FormDataEntryValue }) => {
	try {
		const response = await fetch(`${API_URL}/auth/login`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				email,
				password
			})
		});
		const result = await response.json();

		if (response.status !== 200) {
			return {
				success: false,
				errors: [result.errors]
			};
		}
		return result.data;
	} catch (error: unknown) {
		if (error instanceof Error) {
			return {
				success: false,
				message: 'An error occure while logging in',
				errors: [error.message],
				data: []
			};
		}
		return {
			success: false,
			message: 'An error occure while logging in',
			errors: [],
			data: []
		};
	}
};

export const concreteLogin = async ({ email, password }: { email: FormDataEntryValue; password: FormDataEntryValue }) => {
	const formData = new FormData();
	formData.append('email', email);
	formData.append('password', password);

	const response = await fetch(`${CONCRETE_URL}/api/login`, {
		method: 'POST',
		body: formData
	});

	if (response.status !== 200) {
		throw redirect(300, '/login');
	}

	const result = await response.json();

	return result;
};


export const directusCustomAuth = async ({ email }: { email: string }) => {
	try {
		const res = await fetch(`${API_URL}/concrete-login`, {
			method: 'POST',
			headers: {
			  'Content-Type': 'application/json',
			  'Accept': 'application/json',
			  'Authorization': `Bearer ${API_TOKEN}`
			},
			body: JSON.stringify({
			  email: email,
			})
		  })

		  const data = await res.json();
		  return data;
	} catch (error) {
		console.error(error);
		throw new Error("Unable to login: ");
	}
}

// export const getUserId = async () => {
// 	return auth.subscribe((value) => {
// 		if (value.userId) {
// 			return value.userId;
// 		}
// 	});
// };

export const logout = async () => {
	try {
		// Parallel logout attempts for both services
		const [concreteResult, directusResult] = await Promise.allSettled([
			// Concrete logout
			// This function did not exist on concrete
			// I just created it on the concrete CMS
			// This is a reminder to write documentation for this
			fetch(`${CONCRETE_URL}/api/logout`, {
				method: 'POST',
				credentials: 'include'
			}),
			// Directus logout
			fetch(`${API_URL}/auth/logout`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${API_TOKEN}`
				}
			})
		]);

		// Log any failures but don't throw
		if (concreteResult.status === 'rejected') {
			console.warn('Concrete logout failed:', concreteResult.reason);
		}
		if (directusResult.status === 'rejected') {
			console.warn('Directus logout failed:', directusResult.reason);
		}

		return { success: true };
	} catch (error) {
		console.error('Logout error:', error);
		// Don't throw, allow the client to continue with local cleanup
		return { success: false };
	}
};

export async function validateConcreteSession(sessionKey: string): Promise<boolean> {
	try {
	  const res = await fetch(`${CONCRETE_URL}/api/whoami`, {
		headers: {
		  Cookie: `CONCRETE5=${sessionKey}`
		},
		credentials: 'include'
	  });
  
	  if (!res.ok) return false;
  
	  const json = await res.json();
	  return json?.userID != null;
	} catch (e) {
	  console.error('Session validation error:', e);
	  return false;
	}
  }
  
