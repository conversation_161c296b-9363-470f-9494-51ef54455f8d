import { customFetch } from "$lib/fetch";
import type { IAddExerciseToWorkout, IWorkoutExercise } from "$lib/typings";
import { getExercise, addExercise } from "./exercise.services";

export async function updateWorkoutExercise(
    id: number,
    data: Partial<IWorkoutExercise>,
    locals: App.Locals
) {
    const res = await customFetch({
        url: `/items/workouts_exercises/${id}`,
        options: {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        },
        locals
    });

    return await res.json();
}

export async function addWorkoutExercise(data: IWorkoutExercise, locals: App.Locals) {
    const res = await customFetch({
        url: `/items/workouts_exercises`,
        options: {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        },
        locals
    });
    return await res.json();
}

export async function updateKanban(items: IAddExerciseToWorkout[], locals: App.Locals) {
    const results: any[] = [];

    for(const item of items){
        try {
            if(item.exercise.workout_exercise_id){
                await updateWorkoutExercise(item.exercise.workout_exercise_id, {
                    workout_id: parseInt(item.workout_id),
                    exercise_id: item.exercise.id,
                    position: item.position
                }, locals);
            } else {
                const exercise = await getExercise({ id: item.exercise.id, locals, fields: '*' });

                if (!exercise) {
                    await addExercise({ exercise: item.exercise, locals });
                }

                const workouts_exercises = await addWorkoutExercise({
                    workout_id: parseInt(item.workout_id),
                    exercise_id: item.exercise.id,
                    position: item.position
                }, locals);


                if(typeof workouts_exercises === 'object' && workouts_exercises.data){
                    results.push(workouts_exercises.data);
                }
            }
        } catch (error) {
            console.error(`Something went wrong`);
            throw error;
        }
    }

    return results;

//     const res = await customFetch({
//             url: '/kanban/workout',
//             options: {
//                 method: 'POST',
//                 body: JSON.stringify(items)
//             },
//             locals
//         });

//     if (!res.ok) {
//         throw new Error(`HTTP error! status: ${res.status}`);
//     }

//     const contentType = res.headers.get('content-type');
//   if (contentType && contentType.includes('application/json')) {
//     data = await res.json();
//     console.log(data);
}