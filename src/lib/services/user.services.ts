import { API_TOKEN, API_URL, AUTHENTICATE_ROLE_ID } from "$env/static/private";



export const addUser = async ({email}: { email: string }) => {
    try {
        const res = await fetch(`${API_URL}/users`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${API_TOKEN}`
            },
            body: JSON.stringify({
                email,
                role: AUTHENTICATE_ROLE_ID
            })
        })

        const data = await res.json();
        return data.data;
    } catch (error) {
        throw new Error("Unable to create user: " + error.message);
        
    }
}

export const getUserByEmail = async ({email}: { email: string }) => {
    try {
        const res = await fetch(`${API_URL}/users?filter={"email":{"_eq": "${email}"}}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${API_TOKEN}`
            }
        })
        const data = await res.json();

        if (!data.data && data.data.length === 0) {
            return null;
        }
        return data.data[0];
    } catch (error) {
        throw new Error("Unable to get user: " + error.message);
    }
}



// export const addUser = async ({ email, password } : { email: string, password: string }) => {
//     try {
//         const response = await fetch(`${API_URL}/users/register`, {
//             method: 'POST',
//             headers: {
//                 'Content-Type': 'application/json'
//             },
//             body: JSON.stringify({
//                 email,
//                 password,
//             })
//         });


//         if (response.status !== 204) {
//             throw new Error('Unable to create user');
//         }
//         const data = await response.json();
//         return data;
//     } catch (error: unknown) {
//         if (error instanceof Error) {
//             return {
//                 success: false,
//                 message: 'An error occure while creating user',
//                 errors: [error.message],
//                 data: []
//             };
//         }
//         return {
//             success: false,
//             message: 'An error occure while creating user',
//             errors: [],
//             data: []
//         };
//     }
// }

// export const getUserByEmail = async ({ email } : { email: string }) => {
//     try {
//         const response = await fetch(`${API_URL}/users?filter[email][_eq]=${email}`, {
//             method: 'GET',
//             headers: {
//                 'Content-Type': 'application/json'
//             }
//         });

//         if (response.status !== 200) {
//             throw new Error('Unable to get user');
//         }
//         const results = await response.json();

//         if (!results.data.length) {
//             throw new Error('User not found');
//         }

//         return results.data[0];
//     } catch (error: unknown) {
//         if (error instanceof Error) {
//             return {
//                 success: false,
//                 message: 'An error occure while getting user',
//                 errors: [error.message],
//                 data: []
//             };
//         }
//         return {
//             success: false,
//             message: 'An error occure while getting user',
//             errors: [],
//             data: []
//         };
//     }
// }