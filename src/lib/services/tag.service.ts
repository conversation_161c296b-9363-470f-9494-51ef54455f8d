import { customFetch } from '$lib/fetch';


export const getUserTags = async ({ locals } : { locals: App.Locals }) => {
    try {
        const response = await customFetch({
            url: '/items/tags',
            locals
        })

        const data = await response.json();

        if (response.ok) {
            return {
                success: true,
                data: data.data
            };
        } else {
            return {
                success: false,
                message: 'Failed to retrieve tags',
                errors: data.errors || [],
                data: []
            };
        }
    } catch (error) {
        return {
            success: false,
            message: 'An error occurred when retrieving the tags',
            errors: [error instanceof Error ? error.message : 'Unknown error'],
            data: []
        };
    }
}

export const createTag = async (
    {tag, locals} : 
    {tag: { name: string, color: string }, locals: App.Locals}
) => {
    

    const res = await customFetch({
        url: '/items/tags', 
        locals,
        options:{
            method: 'POST',
            body: JSON.stringify({
                name: tag.name,
                color: tag.color,
            })
        }
    });

    const data = await res.json();

    if (res.status === 200) {
        return {
            success: true,
            data: data.data
        }
    }

    return {
        success: false,
        message: 'Failed to create tag',
        errors: [],
        data: []
    }
}

export const editTag = async ({tagId, tag, locals} : {tag: { name?: string, color?: string }, tagId: string, locals: App.Locals}) => {
    const res = await customFetch({
        url: `/items/tags/${parseInt(tagId)}`, 
        locals,
        options: {
            method: 'PATCH',
            body: JSON.stringify(tag)
        }
    });
    const data = await res.json();

    if (res.status === 200) {
        return {
            success: true,
            data: data.data
        }
    }

    return {
        success: false,
        message: 'Failed to edit tag',
    }
}

export const deleteTag = async ({tagId, locals}: {tagId: string, locals: App.Locals}) => {
    const res = await customFetch({
        url:`/items/tags/${parseInt(tagId)}`, 
        locals,
        options:{
            method: 'DELETE',
        }
    })

    if (res.status === 204) {
        return {
            success: true,
            message: "Tag deleted successfully"
        }
    }

    return {
        success: false,
        message: 'Failed to delete tag',
    }

}

export const deleteTagFromExercise = async ({tagId, exerciseId, locals}: {tagId: string, exerciseId: string, locals: App.Locals}) => {
    try {
        const res = await customFetch({
            url: `/items/tags_exercise`, 
            locals,
            options: {
                method: 'DELETE',
                body: JSON.stringify({
                    query: {
                        filter: {
                            _and: [
                                { tags_id: { _eq: parseInt(tagId) } },
                                { exercise_id: { _eq: parseInt(exerciseId) } }
                            ]
                        }
                    }
                })
            }
        });

        if (res.status === 204) {
            return {
                success: true,
                message: 'Tag removed from exercise successfully'
            };
        } else {
            const data = await res.json();
            return {
                success: false,
                message: 'Failed to remove tag from exercise',
                errors: data.errors || [],
                data: []
            };
        }
    } catch (error) {
        return {
            success: false,
            message: 'An error occurred while removing tag from exercise',
            errors: [error instanceof Error ? error.message : 'Unknown error'],
            data: []
        };
    }
}

export const tagExercise = async ({tagId, exerciseId, locals}: {tagId: string, exerciseId: string, locals: App.Locals}) => {
    const res = await customFetch({
        url:`/items/tags_exercise`, 
        locals,
        options: {
            method: 'POST',
            body: JSON.stringify({ 
                tags_id: parseInt(tagId),
                exercise_id: parseInt(exerciseId)
            })
        }
    })

    const data = await res.json();

    if (res.status === 200) {
        return {
            success: true,
            data: data.data
        }
    }

    if(res.status === 400){
        return {
            success: false,
            message: "Exercise is already tagged!",
        }
    }

    return {
        success: false,
        message: 'Failed to tag exercise'
    }
}

export const getExerciseByName = async ({name, locals}: {name: string, locals: App.Locals}) => {
    const res = await customFetch({
        url: `/items/exercise?filter={ "name": { "_eq": "${name}" }}`,
        locals
    });

    const data = await res.json();

    if(res.status === 200 && data.data.length === 0){
        return {
            success: true,
            message: "Empty"
        }
    } else if (res.status === 200 && data.data.length > 0) {
        return {
            success: true,
            data: data.data
        }
    }
}

export const getExerciseTag = async ({exerciseId, locals}: {exerciseId: string, locals: App.Locals}) => {
    const res = await customFetch({
        url: `/items/tags_exercise?filter={ "exercise_id": { "_eq": ${parseInt(exerciseId)} }}&fields[]=*,tags_id.name,tags_id.color,tags_id.id`,
        locals
    });

    const data = await res.json();

    if(res.status === 200 && data.data.length === 0){
        return {
            success: true,
            message: "Empty"
        }
    } else if (res.status === 200 && data.data.length > 0) {
        return {
            success: true,
            data: data.data
        }
    }
}