<script lang="ts">
	import { Select as SelectPrimitive } from "bits-ui";
	// import CaretSort from "svelte-radix/CaretSort.svelte";
	import { CaretDown } from "svelte-bootstrap-icons";
	import { cn } from "$lib/utils.js";

	type $$Props = SelectPrimitive.TriggerProps;
	type $$Events = SelectPrimitive.TriggerEvents;

	let className: $$Props["class"] = undefined;
	export { className as class };
</script>

<SelectPrimitive.Trigger
	class={cn(
		"border-[#677489] ring-offset-background placeholder:text-[#000000] focus-visible:ring-ring aria-[invalid]:border-destructive data-[placeholder]:[&>span]:text-[#000000] flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
		className
	)}
	{...$$restProps}
>
	<slot />
	<div>
		<CaretDown class="h-4 w-4" />
	</div>
</SelectPrimitive.Trigger>
