<script lang="ts">
	import { Label, Switch as SwitchPrimitive } from "bits-ui";
	import { cn } from "$lib/utils.js";
	import { ArrowBarDown, ArrowDown, ArrowUp } from "svelte-bootstrap-icons";

	type $$Props = SwitchPrimitive.Props;
	type $$Events = SwitchPrimitive.Events;

	let className: $$Props["class"] = undefined;
	export let checked: $$Props["checked"] = undefined;
	export { className as class };
</script>

<div class="flex items-center space-x-3">

	<SwitchPrimitive.Root
	  id="dnd"
	  name="hello"
	  class={cn(
		"focus-visible:ring-foreground bg-gray-300 relative focus-visible:ring-offset-background data-[state=checked]:bg-foreground data-[state=unchecked]:bg-dark-10 data-[state=unchecked]:shadow-mini-inset dark:data-[state=checked]:bg-foreground focus-visible:outline-hidden peer inline-flex h-[36px] min-h-[36px] w-[60px] shrink-0 cursor-pointer items-center rounded-full px-[3px] transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
		className
		)}
		{...$$restProps}
		on:click
		on:keydown
		checked={checked}
	>
			<div class="absolute top-[3px] left-[3px] h-[30px] w-[25px] flex items-center justify-center rounded-full">
				<ArrowUp class="size-6 text-white" />
			</div>
			<div class="absolute top-[3px] right-[3px] h-[30px] w-[25px] flex items-center justify-center rounded-full z-index-0">
				<ArrowDown class="size-6 text-dark-10" />
			</div>

	  <SwitchPrimitive.Thumb
		class="bg-background data-[state=unchecked]:shadow-mini dark:border-background/30 dark:bg-foreground dark:shadow-popover pointer-events-none block size-[30px] shrink-0 rounded-full transition-transform data-[state=checked]:translate-x-6 data-[state=unchecked]:translate-x-0 dark:border dark:data-[state=unchecked]:border"
	  />
	
	 

	</SwitchPrimitive.Root>
  </div>
