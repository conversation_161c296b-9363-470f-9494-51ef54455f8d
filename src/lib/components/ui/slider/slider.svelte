<script lang="ts">
	import { Slider as SliderPrimitive } from 'bits-ui';
	import { cn } from '$lib/utils.js';

	type $$Props = SliderPrimitive.Props;

	let className: $$Props['class'] = undefined;
	export let value: $$Props['value'] = [0];
	export { className as class };
</script>

<SliderPrimitive.Root
	bind:value
	class={cn('relative flex w-full touch-none select-none items-center', className)}
	{...$$restProps}
	let:thumbs
>
	<span class="bg-primary/20 relative h-1.5 w-full grow overflow-hidden rounded-full">
		<SliderPrimitive.Range class="bg-primary absolute h-full" />
	</span>
	{#each thumbs as thumb, index}
		<SliderPrimitive.Thumb {thumb} asChild let:builder>
			<div
				use:builder.action
				{...builder}
				class="border-primary/50 bg-background focus-visible:ring-ring relative flex h-8 w-8 items-center justify-center rounded-sm border text-xs font-semibold shadow transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"
			>
				{Math.round(value[index])}
			</div>
		</SliderPrimitive.Thumb>
	{/each}
</SliderPrimitive.Root>
