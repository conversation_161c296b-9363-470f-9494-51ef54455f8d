<script lang="ts">
	import { flip } from 'svelte/animate';
  import { dndzone, TRIGGERS } from 'svelte-dnd-action';
	import Card from "./Card.svelte";
	import { ThreeDotsVertical } from 'svelte-bootstrap-icons';
	  import * as Popover from "$lib/components/ui/popover";
    import * as Dialog from "$lib/components/ui/dialog";
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
    import { onMount } from "svelte";

	interface IItem {
		id: string;
		aid?: string;
		name: string;
		position?: string;
		workoutId?: string;
	}

	const flipDurationMs = 150;
	
	export let id: string;
	export let name: string;
	export let items: IItem;
	export let onDrop: Function;


	let open = false;

	let selectedWorkout = {
		id: id,
		name: name
	}

	let mounted: boolean = $state(false);

	onMount(() => {
		mounted = true;
		return () => {
			mounted = false;
		};
	});

	function handleDndConsiderCards(e : any) {
		const { items: newItems, info: { id, trigger } } = e.detail;
		items = newItems;
  	}

	async function updateExercise({ id, workoutId, position } : { id: string, workoutId: string, position: number }) {
		const body = JSON.stringify({ workout_id: workoutId, position });

		const response = await fetch(`/api/exercises/${id}`, {
			method: 'PATCH',
			headers: {
				'Content-Type': 'application/json',
			},
			body: body,
		})

		const data = await response.json();


		if (response.status !== 200) {
			
		return data;
		}

		return data;
	}

	async function addExercise({ name, workoutId, position } : { name: string, workoutId: string, position: number }) {
		const body = JSON.stringify({ name, workout_id: workoutId, position });

		const response = await fetch('/api/exercises', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: body,
		})

		const data = await response.json();


		if (response.status !== 200) {
			
		return data;
		}

		return data;
	}




  async function handleDndFinalizeCards(e: any) {
		console.log('finalizing', id);

	const items = e.detail.items.filter((item: IItem, index: number) => {
			item.position = `${index + 1}`;
			item.workoutId = id;
			return item;
		});
	for (const item of items) {
		if (!item.aid) {
			await addExercise({...item})
		}

		await updateExercise({...item});
	}
    onDrop(items);
  }
</script>
<style>
	.wrapper {
		height: 100%;
		width: 100%;
		     /*Notice we make sure this container doesn't scroll so that the title stays on top and the dndzone inside is scrollable*/
        overflow-y: hidden;
	}
	.column-content {
        height: calc(100% - 2.5em);
        /* Notice that the scroll container needs to be the dndzone if you want dragging near the edge to trigger scrolling */
        overflow-y: scroll;
    }
    .column-title {
				height: 2.5em;
			  font-weight: bold;
        display: flex;
        justify-content: center;
        align-items: center;
    }
</style>
<div class='wrapper'>
 	<div class="flex bg-blue-500 justify-between items-center text-white p-6 text-sm text-bold">
		{name}
		{#if mounted}
		<Popover.Root>
			<Popover.Trigger>
		<ThreeDotsVertical class="w-4 h-4" />

			</Popover.Trigger>
			<Popover.Content>
				<ul>
					<li class="hover:bg-blue-200 text-black p-3 cursor-pointer" on:click={() => open = true}>Edit</li>
					<li class="hover:bg-blue-200 text-black p-3 cursor-pointer" on:click={() => {
						if (confirm('Are you sure you want to delete this workout?')) {
							fetch('/api/workouts/' + id, {
								method: 'DELETE',
								headers: {
									'Content-Type': 'application/json',
								},
							})
						}
					}}>Delete</li>
				</ul>
			</Popover.Content>
		</Popover.Root>
		{/if}
	</div>
	<div class="column-content" 
	use:dndzone={{items, flipDurationMs}}
	on:consider={handleDndConsiderCards} 
	on:finalize={handleDndFinalizeCards}
	>
		{#each items as item (item.id)}
           <div animate:flip="{{duration: flipDurationMs}}" >
              <Card name={item.name} />
            </div>
        {/each}
    </div>
</div>


    <Dialog.Root open={open} onOpenChange={(val) => open = val}>
        <Dialog.Content>
            <Dialog.Header>
            <Dialog.Title class="mb-3">Edit Workout {selectedWorkout.name}</Dialog.Title>
            <Dialog.Description >
				<input type="hidden" name="id" value={selectedWorkout.id} />
				<div class="mt-4">
					<Input type="text" name="name" value={selectedWorkout.name} on:change={(e) => selectedWorkout.name = e.target.value} placeholder="program name" required class="max-w-full" />
				</div>
				<div class="mt-4 flex justify-end">
					<Button variant="ghost" type="button" on:click={async() => {
						await fetch('/api/workouts/' + selectedWorkout.id, {
							method: 'PATCH',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({ name: selectedWorkout.name }),
						})
						open = false;

						name = selectedWorkout.name;

					}} class="bg-green-500 text-white">Edit</Button>
				</div>
            </Dialog.Description>
            </Dialog.Header>
        </Dialog.Content>
    </Dialog.Root>