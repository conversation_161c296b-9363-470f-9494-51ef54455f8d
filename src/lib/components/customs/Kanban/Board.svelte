<script lang="ts">
	import { flip } from 'svelte/animate';
	import { dndzone } from 'svelte-dnd-action';
	import Column from "./Column.svelte";
	import { workoutsStore } from '$lib/stores/workoutsStore';
	
	const flipDurationMs = 300;
	
	export let columns: any[];
	export let onFinalUpdate: Function;

	function handleDndConsiderColumns(e: any) {
		columns = e.detail.items;
	}
	function handleDndFinalizeColumns(e: any) {
		onFinalUpdate(e.detail.items);
	}
 	function handleItemFinalize(columnIdx, newItems) {

		console.log("handleItemFinalize", columnIdx, newItems);

		workoutsStore.subscribe(value => {
		});
		// columns[columnIdx].items = newItems;
		columns[columnIdx].exercises = newItems;

		onFinalUpdate([...columns]);
	}
</script>
<style>
		.column {
				height: 100%;
				width: 250px;
				min-width: 250px;
				padding: 0.5em;
				margin: 1em;
				float: left;
				border: 1px solid #8888;
				background-color: white;
		}

		@media (max-width: 768px) {
				.column {
						width: 100%;
						min-width: 100%;
						background-color: red;
						height: 85px;
				}

				.column.active {
					height: 400px;
				}
		}
</style>

<div class="">


</div>

<section class="w-full p- h-[100vh] flex-col overflow-x-scroll" use:dndzone={{items:columns, flipDurationMs, type:'column'}} on:consider={handleDndConsiderColumns} on:finalize={handleDndFinalizeColumns}>
    {#each columns as {id, name, automated_name, exercises}, idx (id)}
    
        <div class="column {idx ==0 ? 'active' : null}" animate:flip="{{duration: flipDurationMs}}" >    
            <Column 
            name={ name ? name : automated_name } 
            id={id} 
            items={exercises} 
            onDrop={(newItems: any[]) => handleItemFinalize(idx, newItems)} />
        </div>
    {/each}
</section>