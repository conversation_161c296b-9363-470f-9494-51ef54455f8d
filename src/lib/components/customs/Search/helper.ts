function buildExerciseQuery(searchValue: string, utilities: string, movement: string, lateral: string, muscle: string, mechanics: string, force: string, apparatus: string, difficulty: { low: number, high: number }, apparatusGroup: string): string {
    let finalUrl = null;
    // const apiUrl = "http://204.235.60.194/exrxapi/v1/allinclusive/exercises";
    // santize the input values
    const search = searchValue !== "" ? searchValue.trim() : null;
    const movementName = lateral != "None" ? lateral : null;
    const movementValue = movement != "None" ? movement : null;
    const utility = utilities != "None" ? utilities : null;
    const mechanicsValue = mechanics != "None" ? mechanics : null;
    const forceValue = force != "None" ? force : null;
    const difficultyValue = `[${difficulty.low},${difficulty.high}]`;
    const muscleGroup = muscle != "None" ? muscle : null;
    const apparatusValue = apparatus != "None" ? apparatus : null;

    if (utilities === "Plyometrics") {
        // Plyometric Query
        finalUrl = `/exercises?exercisename=${search}&intensity=null&movement_name=${movementName}&utility_name=${utility}&difficulty=${difficultyValue}&mechanics=${mechanicsValue}&force="${forceValue}"&movement="${movementValue}"&apparatus_groups_name=null`;
    } else if (utilities == "Flexibility") {
        // Stretch Query
        finalUrl = `/exercises?exercisename=${search}&bodypart=null&musclegroup=${muscleGroup}&utility_name=${utility}&movement_name=${movementName}&apparatus_groups_name=null&synergists=null`;
    } else if (utilities == "Cardio") {
        // Conditioning Query
        finalUrl = `/exercises?exercisename=${search}&subcategory=null&apparatus=${apparatusValue}&utility_name=${utility}&movement_name=${movementName}&apparatus_groups_name=null`;
    } else if (
        utilities == "Basic" ||
        utilities == "Auxiliary" ||
        utilities == "Basic or Auxiliary" ||
        utilities == "Specialized" ||
        utilities == "Power"
    ) {
        // Weight Training Query
        finalUrl = `/exercises?exercisename=${search}&bodypart=null&musclegroup=${muscleGroup}&apparatus=${apparatusValue}&difficulty=${difficultyValue}&utility_name=${utility}&movement_name=${movementName}&movement=${movementValue}&subcategory=null&apparatus_groups_name=null&mechanics=${mechanicsValue}&force=${forceValue}&synergists=null&stabilizers=null`;
    } else if (
        utilities != "Flexibility" &&
        apparatus != "Machine Stretch" &&
        apparatus != "PNF Stretch" &&
        apparatus != "Stretch" &&
        muscle != "None"
    ) {
        // Weight Training Query
        finalUrl = `/exercises?exercisename=${search}&bodypart=null&musclegroup=${muscleGroup}&apparatus=${apparatusValue}&difficulty=${difficultyValue}&utility_name=${utility}&movement_name=${movementName}&movement=${movementValue}&subcategory=null&apparatus_groups_name=null&mechanics=${mechanicsValue}&force=${forceValue}&synergists=null&stabilizers=null`;
    } else if (
        (utilities == "Flexibility" ||
            apparatus == "Machine Stretch" ||
            apparatus == "PNF Stretch" ||
            apparatus == "Stretch") &&
        muscle != "None"
    ) {
        // Stretch Query
        finalUrl = `/exercises?exercisename=${search}&bodypart=null&musclegroup=${muscleGroup}&utility_name=${utility}&movement_name=${movementName}&apparatus_groups_name=null&synergists=null`;
    } else if (
        apparatus == "Bodyweight" &&
        utilities != "Plyometric" &&
        utilities != "Cardio"
    ) {
        // Weight Training Query
        const nullDifficultyValue = null;
        finalUrl = `/exercises?exercisename=${search}&bodypart=null&musclegroup=${muscleGroup}&apparatus=${apparatusValue}&difficulty=${nullDifficultyValue}&utility_name=${utility}&movement_name=${movementName}&movement=${movementValue}&subcategory=null&apparatus_groups_name=${apparatusGroup}&mechanics=${mechanicsValue}&force=${forceValue}&synergists=null&stabilizers=null`;
    } else if (apparatus == "Bodyweight" && utilities == "Plyometric") {
        // Plyometric Query
        finalUrl = `/exercises?exercisename=${search}&intensity=null&movement_name=${movementName}&utility_name=${utility}&difficulty=${difficultyValue}&mechanics=${mechanicsValue}&force=${forceValue}&movement=${movementValue}&apparatus_groups_name=${apparatusGroup}`;
    } else if (apparatus == "Bodyweight" && utilities == "Cardio") {
        // Conditioning Query
        finalUrl = `/exercises?exercisename=${search}&subcategory=null&apparatus=${apparatusValue}&utility_name=${utility}&movement_name=${movementName}&apparatus_groups_name=${apparatusGroup}`;
    } else if (
        apparatus == "Jump Rope" ||
        apparatus == "Climb" ||
        apparatus == "Cycle" ||
        apparatus == "Elliptical" ||
        apparatus == "Row" ||
        apparatus == "Ski" ||
        apparatus == "Step" ||
        apparatus == "Treadmill"
    ) {
        // Conditioning Query
        finalUrl = `/exercises?exercisename=${search}&subcategory=null&apparatus=${apparatusValue}&utility_name=${utility}&movement_name=${movementName}&apparatus_groups_name=${apparatusGroup}`;
    } else if (apparatus == "Medicine Ball") {
        // Plyometric Query
        finalUrl = `/exercises?exercisename=${search}&intensity=null&movement_name=${movementName}&utility_name=${utility}&difficulty=${difficultyValue}&mechanics=${mechanicsValue}&force=${forceValue}&movement=${movementValue}&apparatus_groups_name=${apparatusGroup}`;
    } else if (
        apparatus == "Machine Stretch" ||
        apparatus == "PNF Stretch" ||
        apparatus == "Stretch"
    ) {
        // Stretch Query
        finalUrl = `/exercises?exercisename=${search}&bodypart=null&musclegroup=${muscleGroup}&utility_name=${utility}&movement_name=${movementName}&apparatus_groups_name=${apparatusGroup}&synergists=null`;
    } else if (
        apparatus == "Barbell" ||
        apparatus == "Dumbbell" ||
        apparatus == "Kettlebell" ||
        apparatus == "Rope" ||
        apparatus == "Atlas Stone" ||
        apparatus == "Special Barbell" ||
        apparatus == "Weighted" ||
        apparatus == "Cable" ||
        apparatus == "Lever (plate loaded)" ||
        apparatus == "Lever (selectorized)" ||
        apparatus == "Sled" ||
        apparatus == "Smith" ||
        apparatus == "Band-assisted" ||
        apparatus == "Isometric" ||
        apparatus == "Machine-assisted" ||
        apparatus == "Partner-assisted" ||
        apparatus == "Self-assisted" ||
        apparatus == "Suspended"
    ) {
        // Weight Training Query
        finalUrl = `/exercises?exercisename=${search}&bodypart=null&musclegroup=${muscleGroup}&apparatus=${apparatusValue}&difficulty=${difficultyValue}&utility_name=${utility}&movement_name=${movementName}&movement=${movementValue}&subcategory=null&apparatus_groups_name=${apparatusGroup}&mechanics=${mechanicsValue}&force=${forceValue}&synergists=null&stabilizers=null`;
    } else if (
        (utilities == "None" || utilities != "Plyometric") &&
        (force != "None" || mechanics != "None")
    ) {
        // Weight Training Query
        finalUrl = `/exercises?exercisename=${search}&bodypart=null&musclegroup=${muscleGroup}&apparatus=${apparatusValue}&difficulty=${difficultyValue}&utility_name=${utility}&movement_name=${movementName}&movement=${movementValue}&subcategory=null&apparatus_groups_name=${apparatusGroup}&mechanics=${mechanicsValue}&force=${forceValue}&synergists=null&stabilizers=null`;
    } else if (
        (force != "None" || mechanics != "None") &&
        utilities == "Plyometric"
    ) {
        // Plyometric Query
        finalUrl = `/exercises?exercisename=${search}&intensity=null&movement_name=${movementName}&utility_name=${utility}&difficulty=${difficultyValue}&mechanics=${mechanicsValue}&force=${forceValue}&movement=${movementValue}&apparatus_groups_name=${apparatusGroup}`;
    } else if (
        searchValue.trim() !== "" &&
        utilities == "None" &&
        muscle == "None" &&
        apparatus == "None" &&
        lateral == "None" &&
        movement == "None" &&
        force == "None" &&
        mechanics == "None" &&
        difficulty.low == 0 &&
        difficulty.high == 10
    ) {
        // Exercise Name Only Query
        finalUrl = `/exercises?exercisename=${search}`;
    } else {
        // Global Query
        finalUrl = `/exercises?exercisename=${search}&utility_name=${utility}&movement_name=${movementName}&apparatus_groups_name=${apparatusGroup}`;
    }

    return finalUrl;
}

export { buildExerciseQuery }