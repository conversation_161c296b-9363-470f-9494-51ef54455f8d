<script lang="ts">
	import options from './data.json';
	import * as Select from '$lib/components/ui/select';
	import Button from '$lib/components/ui/button/button.svelte';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Slider } from '$lib/components/ui/slider/index.js';
	import { Label } from '$lib/components/ui/label';
	import * as Dialog from "$lib/components/ui/dialog";
	import ExercisesCard from './Result.svelte';
	import { Search, ArrowClockwise, ArrowLeftShort, ChevronLeft, Disc } from 'svelte-bootstrap-icons';
	import { onMount } from 'svelte';
	import { buildExerciseQuery } from './helper';
	import { toast } from 'svelte-sonner';
	import DragulaCopyOnDrag from '../Browse/DragulaCopyOnDrag.svelte';
	import type { Writable } from 'svelte/store';
	import { resolveTransformedExercises } from '$lib/utils/tags_exercise.utils';
	import { defaultProgram, selectedProgram } from "$lib/stores/generalStore";

	interface IProgram {
        id: string,
        name: string,
        automated_name: string,
        workouts?: any[]
    }

	export let programs: IProgram[];
	export let programId: number;
	export let store: Writable<IProgram[]>;
	export let tagsStore: Writable<any>;
	export let isPremium: boolean;

	let dialogOpen: boolean = false;
	let workouts: any[] = [];
	let selectedExercise: {
        name: string;
        id: string;
        img: string;
        cat: string;
        icon: string;
        position: string;
		invisible?: boolean;
    } | undefined = undefined;
	let selectedWorkout: any | null = null;
	let isMobile: boolean = false;
	let isTablet: boolean = false;

	let searchValue: string = '';
	let difficulty = {
		low: 0,
		high: 10
	};

	
	let tags: [] = [];
	let tagsSelect: any = null;

	let selectedUtilities: any = null;
	let selectedMovement: any = null;
	let selectedLateral: any = null;
	let selectedMuscle: any = null;
	let selectedMechanics: any = null;
	let selectedForce: any = null;
	let selectedApparatus: any = null;
	let selectedTags: [];

	$: utilities = selectedUtilities ? selectedUtilities : 'None';
	$: movement = selectedMovement ? selectedMovement : 'None';
	$: lateral = selectedLateral ? selectedLateral : 'None';
	$: muscle = selectedMuscle ? selectedMuscle : 'None';
	$: mechanics = selectedMechanics ? selectedMechanics : 'None';
	$: force = selectedForce ? selectedForce : 'None';
	$: apparatus = selectedApparatus ? selectedApparatus : 'None';

	defaultProgram.subscribe((info) => {
        if (info) {
            selectedProgram = info;
            return;
        }

        selectedProgram = { 
            value: programs[0].id, 
            label: programs[0].name ? programs[0].name : programs[0].automated_name 
        } 
    });

	let sliderValue = [difficulty.low, difficulty.high];

	// promise
	let promise: any;
	let isUiLoading: boolean = false;

	onMount(() => {
		tagsStore.subscribe((value) => {
			console.log(value)
			tagsSelect = value;
		});

		fetchWorkouts(selectedProgram!.value);
        isMobile = window.outerWidth <= 575;
        isTablet = window.outerWidth > 575 && window.outerWidth < 1024;
	});

	const getApparatusGroupName = () => {
		if (
			apparatus == 'Cable' ||
			apparatus == 'Lever (plate loaded)' ||
			apparatus == 'Lever (selectorized)' ||
			apparatus == 'Sled' ||
			apparatus == 'Smith' ||
			apparatus == 'Machine-assisted'
		) {
			// Machine
			return 'Machine';
		} else if (
			apparatus == 'Barbell' ||
			apparatus == 'Dumbbell' ||
			apparatus == 'Kettlebell' ||
			apparatus == 'Medicine Ball' ||
			apparatus == 'Rope' ||
			apparatus == 'Atlas Stone' ||
			apparatus == 'Special Barbell' ||
			apparatus == 'Weighted'
		) {
			// Free Weight
			return 'Free Weight';
		} else if (
			apparatus == 'Climb' ||
			apparatus == 'Cycle' ||
			apparatus == 'Elliptical' ||
			apparatus == 'Row' ||
			apparatus == 'Ski' ||
			apparatus == 'Step' ||
			apparatus == 'Treadmill'
		) {
			// Cardio Machine
			return 'Cardio Machine';
		} else if (
			apparatus == 'Band-assisted' ||
			apparatus == 'Isometric' ||
			apparatus == 'Bodyweight' ||
			apparatus == 'Machine-assisted' ||
			apparatus == 'Partner-assisted' ||
			apparatus == 'Self-assisted' ||
			apparatus == 'Jump Rope' ||
			apparatus == 'Suspended'
		) {
			// Bodyweight
			return 'Bodyweight';
		} else if (
			apparatus == 'Machine Stretch' ||
			apparatus == 'PNF Stretch' ||
			apparatus == 'Stretch'
		) {
			// Flexibility
			return 'Flexibility';
		} else {
			return 'null';
		}
	};

	const handleSubmit = async () => {
		isUiLoading = true;
		// if input fields contain 'None', set link to null
		const link: string | null =
			[utilities, movement, lateral, muscle, mechanics, force, apparatus].every(
				(v) => v === 'None'
			) && searchValue === ''
				? 'null'
				: buildExerciseQuery(
						searchValue,
						utilities,
						movement,
						lateral,
						muscle,
						mechanics,
						force,
						apparatus,
						difficulty,
						getApparatusGroupName()
					);

		if (link == 'null' && tags.length == 0) {
			toast.error('Please provide either tag or a filter to search');
			isUiLoading = false;
			return [];
		}

		const res = await fetch(
			`/api/filterExercise?link=${encodeURIComponent(link)}&tags=${tags.length == 0 ? 'null' : tags.join(',')}`
		);
		const data = await res.json();

		if (data.success) {
			if (data.data.length === 0) {
				toast.error('No exercises found');
				isUiLoading = false;
				return [];
			}
			promise = [];
			data.data.map((item: any) => {
				promise.push({
					id: item.id ?? item.Exercise_Id,
					name: item.name ?? item.Exercise_Name_Complete_Abbreviation,
					img: item.img ?? item.Small_Img_1,
					icon: item.icon ?? item.Utility_Icon,
					cat: item.cat ?? item.Overall_Category,
					invisible: typeof item.invisible === 'string' ? item.invisible === '0' ? false : true : item.invisible ?? false
				});
			});

			if(!isPremium){
				promise = promise.filter((exercise: any) => !exercise.invisible);
			}

			promise = await resolveTransformedExercises(promise);
			isUiLoading = false;
		} else {
			toast.error(data.message);
			isUiLoading = false;
		}
	};

	function handleSelectionChange(e: { value: string | string[] } | null) {
		if (!e) {
			tags = [];
			return;
		}

		const arr = Array.isArray(e) ? e : [e];
		const set = new Set(arr.map((v) => v.value));

		tags = tags.filter((tag) => set.has(tag));

		for (const v of arr) {
			if (!tags.includes(v.value)) {
				tags.push(v.value);
			}
		}
	}

	function handleBack(): void {
		// set promise to empty to return back
		promise = null;
		tags = [];

		// reset the input fields
		searchValue = '';
		utilities = 'None';
		movement = 'None';
		lateral = 'None';
		muscle = 'None';
		mechanics = 'None';
		force = 'None';
		apparatus = 'None';
		difficulty = {
			low: 0,
			high: 10
		};

		selectedUtilities = null;
		selectedMovement = null;
		selectedLateral = null;
		selectedMuscle = null;
		selectedMechanics = null;
		selectedForce = null;
		selectedApparatus = null;
		selectedTags = [];

	}

	function handleReset() {
		// reset the input fields
		searchValue = '';
		utilities = 'None';
		movement = 'None';
		lateral = 'None';
		muscle = 'None';
		mechanics = 'None';
		force = 'None';
		apparatus = 'None';
		difficulty = {
			low: 0,
			high: 10
		};
		tags = [];

		selectedUtilities = null;
		selectedMovement = null;
		selectedLateral = null;
		selectedMuscle = null;
		selectedMechanics = null;
		selectedForce = null;
		selectedApparatus = null;
		selectedTags = [];
		selectedExercise = undefined;
	}

	$: isDisabledApparatus = (label: string): boolean => {
		if (utilities && !utilities.includes('None')) {
			if (
				utilities === 'Cardio' &&
				!['Climb', 'Cycle', 'Elliptical', 'Row', 'Ski', 'Step', 'Treadmill'].includes(label)
			) {
				return true;
			}

			if (
				utilities === 'Flexibility' &&
				!['Machine Stretch', 'PNF Stretch', 'Stretch'].includes(label)
			) {
				return true;
			}

			if (utilities === 'Plyometrics' && !['Medicine Ball', 'Bodyweight'].includes(label)) {
				return true;
			}
		}
		if (
			muscle &&
			!muscle.includes('None') &&
			['Climb', 'Cycle', 'Elliptical', 'Row', 'Ski', 'Step', 'Treadmill'].includes(label)
		) {
			return true;
		}

		if (
			muscle &&
			!muscle.includes('None') &&
			['Rhomboids', 'Pectoralis Minor', 'Piriformis'].includes(muscle)
		) {
			if (!['Machine Stretch', 'PNF Stretch', 'Stretch'].includes(label)) {
				return true;
			}
		}
		return false;
	};
	$: isDisabledMuscle = (label: string): boolean => {
		if (
			(utilities === 'Auxiliary' || utilities === 'Basic' || utilities === 'Basic or Auxiliary') &&
			['Rhomboids', 'Pectoralis Minor', 'Piriformis'].includes(label)
		) {
			return true;
		}
		return false;
	};
	$: isDisabledUtilities = (label: string): boolean => {
		if (
			muscle &&
			!muscle.includes('None') &&
			['Cardio', 'Kettlebell', 'Plyometrics', 'Power', 'Ski', 'Specialized'].includes(label)
		) {
			return true;
		}

		if (
			muscle &&
			!muscle.includes('None') &&
			['Rhomboids', 'Pectoralis Minor', 'Piriformis'].includes(muscle)
		) {
			if (!['None', 'Flexibility'].includes(label)) {
				return true;
			}
		}

		if (muscle.includes('Transverse Abdominis')) {
			if (!['None', 'Auxiliary'].includes(label)) {
				return true;
			}
		}
		return false;
	};

	function isGroupDisabled(apparatusGroup: any) {
		return apparatusGroup.every((apparatus: any) => isDisabledApparatus(apparatus.name));
	}

	function handleAddExerciseDialogOpen(exercise: any) {
		dialogOpen = true;
		selectedExercise = {
        	icon: exercise.icon,
			cat: exercise.cat,
			name: exercise.name,
			id: exercise.id,
			img: exercise.img,
			invisible: exercise.invisible,
			position: '0'
		};
	}	

async function handleAddExerciseFn(){
		const body = {
        exercise: {
            id: selectedExercise?.id,
            name: selectedExercise?.name,
            cat: selectedExercise?.cat,
            img: selectedExercise?.img,
            icon: selectedExercise?.icon,
			invisible: selectedExercise?.invisible
        },
        position: 0,
        workout_id: selectedWorkout.value
    };

    const response = await fetch('/api/workouts_exercises', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({items: [body]})
    });

    dialogOpen = false;

    const data = await response.json();    

    if (response.ok) {
        // const resWorkouts = await fetchWorkouts(programId.toString());
        
        toast.success('Exercise added successfully');
    } else {
        toast.error(data.message || 'Failed to add exercise');
    }
}

async function fetchWorkouts(programId: string) {
        try {
            const response = await fetch(`/api/workouts?program=${programId}`);
            const data = await response.json();
            workouts = data.data;

            selectedWorkout = {
                value: null,
                label: null
            }
            return data;
        } catch (error) {
            console.error('Error fetching data:', error);
        }
}
</script>

{#if !promise}
	<form class="form-control flex flex-col items-center text-center w-auto h-[calc(100dvh-190px)] sm:h-[calc(100dvh-120px)] overflow-y-auto">
		<!-- input section -->
		<div class="space-y-2 w-full">
			<h2 class="font-semibold uppercase bg-[#323DA3] py-2 text-white">Search</h2>
			<Input type="text" placeholder="Search" class="" bind:value={searchValue} />
		</div>
		<!-- filter section -->
		<div class="space-y-2 w-full mt-2">
			<h2 class="font-semibold uppercase bg-[#323DA3] py-2 text-white">Filter</h2>
			<Select.Root
				selected={selectedUtilities}
				onSelectedChange={(v) => {
					v && (selectedUtilities = v.value);
				}}
			>
				<Select.Trigger>
					<Select.Value placeholder="Utilities" />
				</Select.Trigger>
				<Select.Content>
					{#each options.filter((option) => option.option_name === 'utilities')[0]['options'] as option}
						<Select.Item value={option.name} disabled={isDisabledUtilities(option.name)}
							>{option.name}</Select.Item
						>
					{/each}
				</Select.Content>
			</Select.Root>
			{#if utilities && utilities.includes('Plyometrics')}
				<div class="form-control flex flex-row items-center justify-between gap-2">
					<Select.Root
						selected={selectedMovement}
						onSelectedChange={(v) => {
							v && (selectedMovement = v.value);
						}}
					>
						<Select.Trigger>
							<Select.Value placeholder="Movement Pattern" />
						</Select.Trigger>
						<Select.Content>
							{#each options.filter((option) => option.option_name === 'movement')[0]['options'] as option}
								<Select.Item value={option.name}>{option.name}</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
				</div>
			{/if}
			<div class="form-control flex flex-row items-center justify-between gap-2">
				<Select.Root
					selected={selectedLateral}
					onSelectedChange={(v) => {
						v && (selectedLateral = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="Lateral Pattern" />
					</Select.Trigger>
					<Select.Content>
						{#each options.filter((option) => option.option_name === 'lateral')[0]['options'] as option}
							<Select.Item value={option.name}>{option.name}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
			<div
				class="form-control flex flex-row items-center justify-between gap-[20px] {utilities && utilities.includes(
					'Plyometrics'
				) ||
				utilities.includes('Cardio') ||
				utilities.includes('Power') ||
				utilities.includes('Specialized') ||
				utilities.includes('Kettlebell') ||
				apparatus.includes('Climb') ||
				apparatus.includes('Cycle') ||
				apparatus.includes('Elliptical') ||
				apparatus.includes('Row') ||
				apparatus.includes('Ski') ||
				apparatus.includes('Step') ||
				apparatus.includes('Treadmill')
					? 'hidden'
					: ''}"
			>
				<Select.Root
					selected={selectedMuscle}
					onSelectedChange={(v) => {
						v && (selectedMuscle = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="Muscle Group" />
					</Select.Trigger>
					<Select.Content class="overflow-y-auto max-h-[10rem]">
						{#each options.filter((option) => option.option_name === 'muscle')[0]['options'] as option}
							<Select.Group>
								<Select.Label>{option.group}</Select.Label>
								{#each option.muscles as muscle}
									<Select.Item value={muscle.name} disabled={isDisabledMuscle(muscle.name)}
										>{muscle.name}</Select.Item
									>
								{/each}
							</Select.Group>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
			<div
				class="form-control flex flex-row items-center justify-between gap-2 {utilities && utilities.includes(
					'Cardio'
				) ||
				utilities === 'Flexibility' ||
				muscle.includes('Rhomboids') ||
				muscle.includes('Pectoralis Minor') ||
				muscle.includes('Piriformis')
					? 'hidden'
					: ''}"
			>
				<Select.Root
					selected={selectedMechanics}
					onSelectedChange={(v) => {
						v && (selectedMechanics = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="Mechanics" />
					</Select.Trigger>
					<Select.Content>
						{#each options.filter((option) => option.option_name === 'mechanics')[0]['options'] as option}
							<Select.Item value={option.name}>{option.name}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
			<div
				class="form-control flex flex-row items-center justify-between gap-2 { utilities && utilities.includes(
					'Cardio'
				) ||
				utilities === 'Flexibility' ||
				muscle.includes('Rhomboids') ||
				muscle.includes('Pectoralis Minor') ||
				muscle.includes('Piriformis')
					? 'hidden'
					: ''}"
			>
				<Select.Root
					selected={selectedForce}
					onSelectedChange={(v) => {
						v && (selectedForce = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="Force" />
					</Select.Trigger>
					<Select.Content>
						{#each options.filter((option) => option.option_name === 'force')[0]['options'] as option}
							<Select.Item value={option.name}>{option.name}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
			<div class="form-control flex flex-row items-center justify-between gap-2">
				<Select.Root
					selected={selectedApparatus}
					onSelectedChange={(v) => {
						v && (selectedApparatus = v.value);
					}}
				>
					<Select.Trigger>
						<Select.Value placeholder="Apparatus" />
					</Select.Trigger>
					<Select.Content class="overflow-y-auto max-h-[10rem]">
						{#each options.filter((option) => option.option_name === 'apparatus')[0]['options'] as option}
							<Select.Group>
								<Select.Label
									class={isGroupDisabled(option.apparatus)
										? 'text-gray-400 cursor-not-allowed'
										: ''}>{option.group}</Select.Label
								>
								{#each option.apparatus as apparatus}
									<Select.Item value={apparatus.name} disabled={isDisabledApparatus(apparatus.name)}
										>{apparatus.name}</Select.Item
									>
								{/each}
							</Select.Group>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
			{#if apparatus && apparatus.includes('Bodyweight') || apparatus.includes('Suspended')}
				<div class="form-control flex flex-row items-center justify-between gap-2">
					<div class="flex-1 text-start space-y-2">
						<Label for="terms">Difficulty</Label>
						<Slider bind:value={sliderValue} max={10} step={1} />
					</div>
				</div>
			{/if}
		</div>

		<!-- tag section -->
		<div class="space-y-2 w-full mt-[20px]">
			<h2 class="font-semibold uppercase bg-[#323DA3] py-2 text-white">Tags</h2>
			<Select.Root selected={selectedTags} onSelectedChange={handleSelectionChange} multiple={true}>
				<Select.Trigger>
					<Select.Value placeholder="Tags" />
				</Select.Trigger>
				<Select.Content>
					{#each tagsSelect as tag}
						<Select.Item value={tag.id}>{tag.name}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		<!-- search and rest buttons -->
		<div class="space-y-2 w-auto mt-[20px]">
			<div class="flex flex-row items-center gap-2">
				<Button class="bg-[#FC570C]" on:click={handleReset} disabled={isUiLoading}>
					<ArrowClockwise class="mr-2 h-4 w-4" />
					Reset
				</Button>
				<Button class="bg-[#FC570C]" on:click={handleSubmit} disabled={isUiLoading}>
					{#if isUiLoading}
					<Disc class="mr-2 h-4 w-4 animate-spin" />
					Search
					{:else}
					<Search class="mr-2 h-4 w-4" />
					Search
					{/if}
				</Button>
			</div>
		</div>
	</form>
{:else}
	<div class="w-full md:w-auto h-[calc(100dvh-190px)] sm:h-[calc(100dvh-120px)]">
		<Button variant="link" on:click={handleBack}>
			<ChevronLeft />
		</Button>
		<div>
			{#if promise && promise.length > 0}
				{#if promise.length >= 30}
					<div class="w-full flex items-center justify-center">
						<div class="flex flex-col items-center justify-center text-center">
							<p>Too many exercises found.</p>
							<p>Please be more specific in your search or apply additional filters.</p>
						</div>
					</div>
				{:else}
					{#if isMobile || isTablet }
						{#each promise as prom}
							<ExercisesCard exercise={prom} onClick={() => handleAddExerciseDialogOpen(prom)} />
						{/each}
					{:else}
						<DragulaCopyOnDrag items={promise} onClick={handleAddExerciseDialogOpen} />
					{/if}
				{/if}
			{:else}
				<p>No results found.</p>
			{/if}
		</div>
	</div>
	<Dialog.Root open={dialogOpen} onOpenChange={(val) => dialogOpen = val}>
		<Dialog.Content>
			<Dialog.Header>
			<Dialog.Title class="mb-3">Add To Workout</Dialog.Title>
			<Dialog.Description >
				<div class="flex flex-col gap-2 mb-3">
					<label for="program">Select a program</label>
						<Select.Root bind:selected={$selectedProgram!} onSelectedChange={(val: any) => {
						fetchWorkouts(val?.value);
						$selectedProgram = val;
					}}>
						<Select.Trigger class="w-full">
							<Select.Value placeholder="Select a program" />
						</Select.Trigger>
						<Select.Content>
							{#each programs as program}
								<Select.Item value={program.id}>{program.name ? program.name : program.automated_name}</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
				</div>
				<div class="flex flex-col gap-2 mb-6">
	
					<label for="workout">Select a workout</label>
					<Select.Root onSelectedChange={(val) => { selectedWorkout = val}}>
					<Select.Trigger class="w-full">
						<Select.Value placeholder="Select a workout" />
					</Select.Trigger>
					<Select.Content>
						{#each workouts as workout}
							<Select.Item value={workout.id}>{workout.name ? workout.name : workout.automated_name}</Select.Item>
						{/each}
					</Select.Content>
					</Select.Root>
				</div>
				<Button class="bg-blue-500" on:click={handleAddExerciseFn}>Add</Button>
			</Dialog.Description>
			</Dialog.Header>
		</Dialog.Content>
	</Dialog.Root>
{/if}
