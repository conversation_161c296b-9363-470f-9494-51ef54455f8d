<script lang="ts">
    import Button from "$lib/components/ui/button/button.svelte";
    import { PlusCircle, Pencil, Clipboard2Plus, ArrowRepeat, Trash, Disc, BoxArrowInLeft, QuestionCircle, Globe, Copy, Journals } from "svelte-bootstrap-icons";
    import Board from "$lib/components/customs/Board/Board.svelte";
    import DragDrop from "$lib/components/DragDrop.svelte";
    import ExerciseDetailsPage from "$lib/components/customs/ExerciseDetailsPage/ExerciseDetailsPage.svelte";
    import { isBoardScrolled, showExerciseDetailsPage, defaultTab } from "$lib/stores/generalStore";
    import Header from "$lib/components/customs/Header/Header.svelte";
    import * as Drawer from "$lib/components/ui/drawer";
    import * as Dialog from "$lib/components/ui/dialog";
    import { goto } from "$app/navigation";
    import { toast } from "svelte-sonner";
    import { writable, type Writable } from "svelte/store";
    import { Input } from "$lib/components/ui/input";
    import { onMount, onDestroy } from "svelte";
    import { showDesignPage, selectedWorkout, selectedProgram, layoutAction } from "$lib/stores/generalStore";
    import { writeClipboard } from "$lib/utils";

    import type { IProgram, IWorkout, IExercise, IAddExerciseToWorkout } from "$lib/typings";
    import { updateKanban, deleteExerciseWorkout } from "$lib/utils/workouts_exercises.utils";
    

    let { data, isDeleteMode, tagStore, workoutsStore } = $props<{
      workoutsStore: Writable<IWorkout[]>;
      data: any;
      isDeleteMode?: boolean;
      tagStore: any;
    }>();

    console.log('Workouts2: ', $workoutsStore);

    let programName = $state(data.program.name);
    let programAutoName = $state(data.program.automated_name);
    let openBottomDrawer: boolean = $state(false)
    let openEditProgramDialog = $state(false);
    let isLoading = $state(false);
    let isLogoutLoading: boolean = $state(false);
    let isMobile: boolean = $state(false);
    let isTablet: boolean = $state(false);
    let isDesktop: boolean = $state(false);
    let isEditMode: boolean = $state(false);


    onMount(() => {  
      $inspect(data.program); 
      const checkScreenSize = () => {
        isDesktop = window.innerWidth >= 1024;
        isMobile = window.innerWidth < 768;
        isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;	  
      };

      checkScreenSize();
      window.addEventListener('resize', () => {
        checkScreenSize();
      });
      
      return () => {
        window.removeEventListener('resize', checkScreenSize);
      };
  })

  const unsubscribe = layoutAction.subscribe((action) => {
    if (!action) return;

    switch (action) {
      case 'addWorkout':
        console.log('Trying to add workout');
        handleAddWorkout();
        break;
      case 'editWorkout':
        isEditMode = !isEditMode;
        break;
      case 'duplicateProgram':
        
        break;
      case 'copyProgramLink':

        break;
      case 'toggleDeleteMode':
        isDeleteMode = !isDeleteMode;
        break;
      case 'help':
        break;
      case 'logout':
        handleLogout();
        break;
    }

    
    layoutAction.set(null);
  });

  onDestroy(() => {
    unsubscribe();
  });

  $effect(() => {
    if ($workoutsStore) {
      programAutoName = data.program.automated_name;
      programName = data.program.name;
    }
  });

function formatProgramName(startMonth: string, startYear: number, endMonth: string, endYear: number, alphabet: string) {
        if (startYear === endYear) {
            return startMonth === endMonth
                ? `${startMonth} ${startYear} ${alphabet || ''}`
                : `${startMonth} ${endMonth} ${startYear || ''}`;
        } else {
            return `${startMonth} ${startYear} / ${endMonth} ${endYear} ${alphabet || ''}`;
    }
}

function renameProgram() {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.toLocaleString('en-us', { month: 'short' });

  // Parse the existing automated name
  const nameParts = data.program.automated_name.split(' ');


  let startMonth: string = "";
  let startYear: number = 0;
  let alphabet: string = "";

  if (Number.isFinite(+nameParts[0])) {
      startMonth = nameParts[1];
      startYear = parseInt(nameParts[0]);
      alphabet = nameParts[2] || '';
  } else if (Number.isFinite(+nameParts[1])) {
      startYear = parseInt(nameParts[1]);
      startMonth = nameParts[0];
      alphabet = nameParts[5] || '';
  } else {
      startYear = parseInt(nameParts[2]);
      startMonth = nameParts[0];
      alphabet = nameParts[3] || '';
  }

  // Extract the end date
  const newProgramName = formatProgramName(startMonth, startYear, month, year, alphabet);

    fetch('/api/programs/'+ data.program.id, {
          method: 'PATCH',
          headers: {
              'Content-Type': 'application/json'
          },
          body: JSON.stringify({
              automated_name: newProgramName
          })
      })
}
  
    // Event handlers for DragDrop component
  async function handleDataUpdate(event: CustomEvent<{ workouts: IWorkout[] }>) {
      console.log('handleDataUpdate', $workoutsStore)
      const updatedWorkoutsFromEvent = event.detail.workouts;
      const itemsToUpdate: IAddExerciseToWorkout[] = [];

      updatedWorkoutsFromEvent.forEach(workout => {
          if (workout.exercises) {
              workout.exercises.forEach((exercise, index) => {  
                  itemsToUpdate.push({
                      exercise: {
                        ...exercise,
                        id: exercise.exercise_id,
                        workout_exercise_id: exercise.id
                      }, // Pass the full exercise object
                      workout_id: workout.id,
                      position: index + 1
                  });
              });
          }
      });

      // Optimistically update the UI
      workoutsStore.set(updatedWorkoutsFromEvent);

      try {
          if (itemsToUpdate.length > 0) {
            await updateKanban(itemsToUpdate);
          }
      } catch (error) {
          console.error("Error updating kanban:", error);
      }
  }

  const handleEditProgramClick = async () => {
    
    const res = await fetch('/api/programs/'+ data.program.id, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: data.program.name
        })
    })

    const temp = await res.json();

    if (!temp.success) {
        toast.error(temp.message);
        return;
    }

    // const temp = programs.filter((program) => {
    //     if (program.id === currentProgram.id) {
    //         program.name = data.data.name
    //     }

    //     return program;
    // });

    // programs = temp

    openEditProgramDialog = false;
    
    toast.success(temp.message);

};

  const handleAddWorkout = async () => {
    isLoading = true;
    const response = await fetch('/api/workouts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({program_id: data.program.id, last: data.program.last})
    });

    const workout = await response.json();
    
    data.program.last = workout.next

    selectedWorkout.set({value: workout.data.id, label: workout.data.name || workout.data.automated_name })
    selectedProgram.set({value: data.program.id, label: data.program.name || data.program.automated_name })

    workoutsStore.set([ ...$workoutsStore, workout.data ]);
    toast.success(workout.message);
    renameProgram();

    openBottomDrawer = false;
    isLoading = false;
    }

    async function handleLogout() {
        isLogoutLoading = true;
        try {
            const response = await fetch('/api/auth/logout', {
                method: 'POST'
            });

            if (response.ok) {
                isLogoutLoading = false;
                toast.success('Successfully logged out');
                goto('/login');
            } else {
                toast.error('Failed to logout');
            }
        } catch (error) {
            console.error('Logout error:', error);
            toast.error('Failed to logout');
        } finally {
            isLogoutLoading = false;
        }
    }

  async function handleDeleteWorkout(workoutId: string | number, workoutName: string) {
      try {
          const res = await fetch('/api/workouts/' + workoutId, {
              method: 'DELETE'
          });
          const data = await res.json();
          if (data?.success) {
              toast.success(data.message);
              const currentWorkouts = $workoutsStore;
              const updatedWorkouts = currentWorkouts.filter((workout: IWorkout) => workout.id !== workoutId);
              const temp: any = []

              updatedWorkouts.forEach((w: IWorkout, i: number) => {
                  const code = String.fromCharCode(65+i)
                  temp.push({
                      ...updatedWorkouts[i],
                      position: code,
                      automated_name:  `Workout ${code}`
                  })
              
              })

            await fetch(`/api/workouts`, { 
                method: 'PATCH', 
                headers: {
                    'Content-Type': 'Application/json'
                }, 
                body: JSON.stringify({ items: temp })
            });
            
            workoutsStore.set(temp);

              isDeleteMode = false;
          } else {
              toast.error(data.message || `Failed to delete workout '${workoutName}'.`);
          }
      } catch (error) {
          console.error("Error deleting workout:", error);
          toast.error(`Error deleting workout '${workoutName}'.`);
      }
  }
  
  async function handleDeleteItem(event: CustomEvent<{ workoutId: string | number, exerciseId: string | number | undefined, workoutName: string, exerciseName: string }>) {
      const { workoutId, exerciseId, workoutName, exerciseName } = event.detail;

      if (exerciseId === undefined) {
          // This is a workout deletion
          if (!workoutId) {
              toast.error("Cannot delete workout: Workout ID is missing.");
              return;
          }
          await handleDeleteWorkout(workoutId, workoutName);
      } else {
          // This is an exercise deletion
          if (!workoutId) { // Should always have workoutId if exerciseId is present, but good to check
              toast.error("Cannot delete exercise: Workout ID is missing.");
              return;
          }
          try {
              const success = await deleteExerciseWorkout(exerciseId as number); // workout_exercise_id

              if (success) {
                  workoutsStore.update(currentWorkouts => {
                      const workoutIndex = currentWorkouts.findIndex(w => w.id === workoutId);
                      if (workoutIndex !== -1 && currentWorkouts[workoutIndex].exercises) {
                          const updatedExercises = currentWorkouts[workoutIndex].exercises.filter(ex => ex.workout_exercise_id !== exerciseId);
                          const updatedWorkout = {
                              ...currentWorkouts[workoutIndex],
                              exercises: updatedExercises
                          };
                          const newWorkouts = [...currentWorkouts];
                          newWorkouts[workoutIndex] = updatedWorkout;
                          return newWorkouts;
                      }
                      return currentWorkouts;
                  });
                  toast.success(`Successfully deleted '${exerciseName}' from workout '${workoutName}'!`);
              } else {
                  toast.error(`Failed to delete '${exerciseName}' from workout '${workoutName}'.`);
              }
          } catch (error) {
              console.error("Error deleting exercise:", error);
              toast.error(`Error deleting exercise '${exerciseName}'.`);
          }
      }
  }
  
    function handleAddExerciseClick() {
      // Navigate to exercises page/tab
      showDesignPage.set(!$showDesignPage);
    }

    async function handleCopyProgram() {
      const programToCopy = data.program;
      const copied = await writeClipboard(programToCopy);
      if(copied) {
          toast.success('Program copied to clipboard!');
      } else {
          toast.error('Failed to copy program. Please try again.');
      }
    }
  </script>

<Header 
    backIcon 
    onBackIconClick={() => { goto('/programs') }}
    title={data.program.automated_name}
    subtitle={data.program.name}
    threeDotsIcon
    onThreeDotsIconClick={() => openBottomDrawer = !openBottomDrawer}
    onArrowLeftRightClick={() => {
      showDesignPage.set(!$showDesignPage);
      defaultTab.set('exercises');
      }}
/>
  
  <div class="w-full h-full">
    {#if $showExerciseDetailsPage}
      <div class="absolute inset-0 z-10 bg-[#F0F2FF] overflow-y-auto">
        <ExerciseDetailsPage workoutStore={workoutsStore} tagStore={tagStore} isPremium={data.uData?.isPremium} programs={data.programs}/>
      </div>
    {:else}
      <div class="w-full h-full">
        <div class="h-[calc(100dvh-190px)] sm:h-[calc(100dvh-120px)]">
          {#if isMobile || isTablet}
            <DragDrop 
              workoutsStore={workoutsStore}
              on:updateData={handleDataUpdate} 
              on:deleteItem={handleDeleteItem} 
              programId={data.program.id}
              programName={programName} 
              programAutoName={programAutoName} 
              {isDeleteMode} 
              {isEditMode}
            />
          {:else}
            <Board 
              workoutsStore={workoutsStore} 
              programId={data.program.id} 
              {isDeleteMode} 
              programName={data.program.name} 
              programAutoName={data.program.automated_name} 
              program={data.program} 
            />
          {/if}
        </div>
        <Button 
          class="flex bg-[#FC570C] focus:bg-[#FC570C] active:bg-[#FC570C] text-white fixed bottom-[90px] sm:bottom-2 right-2 z-40 {$isBoardScrolled ? 'rounded-full z-50 w-12 h-12 p-0 justify-center' : ''}" 
          on:click={handleAddExerciseClick}
        >
          {#if $isBoardScrolled} 
            <PlusCircle class="size-4" />  
          {:else}
            Add Exercise
          {/if}
        </Button>
      </div>
    {/if}
  </div>
  
  {#if $showExerciseDetailsPage && !isDesktop}
    <div class="fixed inset-0 z-50 bg-[#F0F2FF] overflow-y-auto">
      <ExerciseDetailsPage workoutStore={workoutsStore} tagStore={tagStore} isPremium={data.uData?.isPremium} programs={data.programs}/>
    </div>
  {/if}

  <Drawer.Root open={openBottomDrawer} onOpenChange={(val) => { openBottomDrawer = val }}>
    <Drawer.Content class="flex flex-col gap-2 p-4 max-w-[850px] w-full mx-auto sm:bottom-[50px]">
      <Button variant="ghost" disabled={isLoading} class="flex gap-4 {isLoading ? 'text-[#313da3]' : ''} bg-slate-200 rounded justify-start items-center text-[16px]" on:click={ handleAddWorkout }>
       {#if isLoading}
        <Disc class="size-6 animate-spin" />
        Adding
       {:else}
        <Clipboard2Plus class="size-6" />
        Add Workout
        {/if}
      </Button>
      <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" on:click={() => {
        isEditMode = !isEditMode;
        openBottomDrawer = false;
      }}>
        <Pencil class="size-6" />
        {isEditMode ? 'Cancel Edit' : 'Edit Workout mode'}
      </Button>
      <!-- <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" on:click={ () => {} }>
        <Copy class="size-6" />
        Duplicate Program
      </Button> -->
      <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" on:click={handleCopyProgram}>
        <Journals class="size-6" />
        Copy Program with Link
      </Button>
      <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" on:click={() => {
        isDeleteMode = !isDeleteMode;
        openBottomDrawer = false;
      }}>
        <Trash class="size-6" />
        {isDeleteMode ? 'Cancel Delete' : 'Delete Workout mode'}
      </Button>
      <Button variant="ghost" class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]" on:click={ () => {} }>
        <QuestionCircle class="size-6" />
        Help
      </Button>
      <Button 
        variant="ghost" 
        class="flex gap-4 bg-slate-200 rounded justify-start items-center text-[16px]"
        on:click={() => {
            handleLogout();
           if(!isLogoutLoading){
            openBottomDrawer = false;
           }
        }}
        disabled={isLogoutLoading}
      >
        {#if isLogoutLoading}
            <Disc class="mr-2 animate-spin"/> 
        {:else}
            <BoxArrowInLeft class="size-6" />
        {/if}
        Logout
      </Button>
    </Drawer.Content>
  </Drawer.Root>
  
  <Dialog.Root open={openEditProgramDialog} onOpenChange={(val) => { openEditProgramDialog = val }}>
    <Dialog.Content class="rounded-[2rem] top-[230px]">
      <Dialog.Header>
        <Dialog.Title>Edit program name</Dialog.Title>
      </Dialog.Header>
      <input type="hidden" name="id" value={data.program.id} />
      <div class="mt-4">
        <Input type="text" name="name" value={data.program.name} on:blur={(e) => {data.program.name = e.target.value}} placeholder="program name" required 
          class="max-w-full border-white border-b-gray-600 focus:border-white focus:outline-white focus:ring-0 focus:ring-offset-0 focus:ring-offset-gray-800 focus:ring-white" />
      </div>
      <div class="mt-4 flex justify-between items-center">
        <Button variant="ghost" type="button" class="text-[#FC570C] font-bold text-sm" on:click={() => { openEditProgramDialog = false; data.program.name = name }}>Cancel</Button>
        <Button variant="ghost" type="button" class="text-[#FC570C] font-bold text-sm" on:click={handleEditProgramClick}>Save</Button>
      </div>
    </Dialog.Content>
  </Dialog.Root>