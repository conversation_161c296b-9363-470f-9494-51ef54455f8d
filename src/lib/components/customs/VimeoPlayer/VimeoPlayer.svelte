<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import Hls from 'hls.js';
	import { ArrowsFullscreen, Pause, PauseBtnFill, PauseFill, Play, PlayBtnFill, PlayFill, VolumeUp } from 'svelte-bootstrap-icons';

  // --- Props ---
  let { vimeoUrl } = $props<{
    vimeoUrl: string;
  }>();

  // --- State ---
  // We need one reference for the video element itself
  let videoElement: HTMLVideoElement | null = $state(null);
  // And another reference for the container div (for fullscreen)
  let videoContainerElement: HTMLDivElement | null = $state(null);

  let hls: Hls | null = $state(null);

  // Playback state
  let isPlaying = $state(false);
  let currentTime = $state(0);
  let duration = $state(0); // Will be updated when metadata loads

  // Volume state
  let volume = $state(1); // 0 to 1
  let isMuted = $state(false);
  let lastVolume = $state(1); // Store volume before muting

  // Quality state (from your original code)
  let qualityLevels: { index: number; label: string }[] = $state([]);
  let selectedLevel = $state(-1); // -1 for Auto

  // UI state
  let isFullscreen = $state(false);


  // --- Lifecycle ---
  onMount(() => {
    // Ensure both elements are available
    if (!videoElement || !videoContainerElement) {
      console.error('Video or container element not found on mount.');
      // Optionally hide player or show error message
      return;
    }

    // --- Initial State Setup (before attaching HLS/source) ---
    // Set muted state for autoplay reliability.
    // It's often best to set the DOM property directly here.
    videoElement.muted = true;
    isMuted = true;
    volume = videoElement.volume; // This will be 0 because it's muted

    // --- HLS.js Initialization ---
    if (Hls.isSupported()) {
      hls = new Hls();
      hls.loadSource(vimeoUrl);
      hls.attachMedia(videoElement); // <-- Attach to the actual video element
      console.log('HLS.js initialized and attached.');

      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS Manifest Parsed');
        qualityLevels = hls!.levels.map((level, i) => ({
          index: i,
          label: `${level.height}p`
        }));
        qualityLevels.unshift({ index: -1, label: 'Auto' });
        console.log('Available quality levels:', qualityLevels);

         // Some HLS setups might only report duration accurately after parsed
         if (videoElement!.duration && isFinite(videoElement!.duration)) {
             duration = videoElement!.duration;
             console.log('Initial duration from MANIFEST_PARSED:', duration);
         }

         // Start playback automatically if possible
         videoElement!.play().catch(error => {
             console.warn('Autoplay prevented:', error);
             // Handle cases where autoplay is blocked (e.g., unmute button prompt)
         });

      });

       hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
          console.log('HLS level switched to:', data.level);
           // Update selectedLevel state if HLS switches automatically (e.g., from Auto)
           if (hls && hls.currentLevel !== selectedLevel) {
               selectedLevel = hls.currentLevel;
                console.log('Selected level state updated by HLS switch:', selectedLevel);
           }
       });


      hls.on(Hls.Events.ERROR, (event, data) => {
          console.error('HLS Error:', data);
          if (data.fatal) {
              switch(data.type) {
                  case Hls.ErrorTypes.NETWORK_ERROR:
                      console.error('Fatal network error encountered, trying to recover');
                      hls!.recoverMediaError();
                      break;
                  case Hls.ErrorTypes.MEDIA_ERROR:
                      console.error('Fatal media error encountered, trying to recover');
                       hls!.recoverMediaError();
                      break;
                  default:
                      // Cannot recover
                      console.error('Fatal unrecoverable HLS error, destroying HLS instance.');
                      hls!.destroy();
                      hls = null; // Clear the instance
                      // Optionally try to use native playback if possible?
                      if (videoElement && videoElement.canPlayType('application/vnd.apple.mpegurl') && vimeoUrl) {
                          videoElement.src = vimeoUrl;
                          console.log('Attempting native HLS playback after fatal HLS.js error.');
                           // Attempt autoplay again for native
                           videoElement!.play().catch(error => {
                                console.warn('Native autoplay prevented:', error);
                           });
                      } else {
                           console.error('No recovery option available.');
                          // Display user-friendly error message or hide player
                      }
                      break;
              }
          }
      });


    } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
      // Native HLS support (Safari, iOS)
      videoElement.src = vimeoUrl; // <-- Set source on the actual video element
      console.log('Using Native HLS');
       // Native HLS often reports duration sooner
         if (videoElement!.duration && isFinite(videoElement!.duration)) {
             duration = videoElement!.duration;
             console.log('Initial duration from Native HLS:', duration);
         }
         // Hide quality select for native HLS as manual switching isn't supported
         qualityLevels = []; // Clear the array to hide the select element

         // Attempt autoplay for native
         videoElement!.play().catch(error => {
             console.warn('Native autoplay prevented:', error);
         });

    } else {
        console.error('HLS is not supported in this browser.');
        // Potentially hide player or show error message
        // Clear quality levels just in case
        qualityLevels = [];
    }

    // --- Video Element Event Listeners ---
    // Add listeners to the actual video element
    const video = videoElement;
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('volumechange', handleVolumeChange);
    video.addEventListener('ended', handleEnded);
    video.addEventListener('durationchange', handleLoadedMetadata);
    video.addEventListener('error', handleVideoError);


    // Add listeners for fullscreen change on the document
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    // Initial state sync based on the element's final state after setup
    // isPlaying = !video.paused; // Will be true if autoplay succeeded immediately
    // volume = video.volume; // Already synced after setting muted
    // isMuted = video.muted; // Already synced after setting muted
    // currentTime = video.currentTime; // Will be updated by timeupdate soon after play
    // duration updated by loadedmetadata/durationchange

     console.log('Component mounted. Initial state:', { isPlaying, currentTime, duration, volume, isMuted });

  });

  onDestroy(() => {
    if (hls) {
      hls.destroy();
      hls = null;
    }

    // --- Clean up Video Element Event Listeners ---
    // Remove listeners from the actual video element
    if (videoElement) {
      const video = videoElement;
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('volumechange', handleVolumeChange);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('durationchange', handleLoadedMetadata);
      video.removeEventListener('error', handleVideoError);
    }

       // Remove listeners for fullscreen change from document
       document.removeEventListener('fullscreenchange', handleFullscreenChange);
       document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
       document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
       document.removeEventListener('msfullscreenchange', handleFullscreenChange);
  });

  // --- Event Handlers (Video Element) ---
  function handlePlay() {
    isPlaying = true;
    console.log('Video playing');
  }

  function handlePause() {
    isPlaying = false;
    console.log('Video paused');
  }

  function handleTimeUpdate() {
    currentTime = videoElement!.currentTime;
  }

  function handleLoadedMetadata() {
    if (videoElement && videoElement.duration && isFinite(videoElement.duration)) {
         duration = videoElement.duration;
         console.log('Metadata loaded, duration:', duration);
     } else {
         duration = 0;
         console.log('Metadata loaded, duration not finite:', videoElement?.duration);
     }
  }

  function handleVolumeChange() {
    if (!videoElement) return;
    volume = videoElement.volume;
    isMuted = videoElement.muted;
    // If volume changes *while not muted* and it's not 0, update lastVolume
    // Also update lastVolume when volume is set, but not muted
    if (!isMuted && volume > 0) {
        lastVolume = volume;
    }
     console.log('Volume changed:', { volume, isMuted, lastVolume });
  }

   function handleEnded() {
       isPlaying = false;
       console.log('Video ended');
       // Optional: Seek back to the beginning if needed for replay button etc.
       // if (videoElement) videoElement.currentTime = 0;
   }

    function handleVideoError() {
        console.error('Native video element error:', videoElement?.error);
        // Potentially show error message to user
        // Check error code for specifics: https://developer.mozilla.org/en-US/docs/Web/API/MediaError/code
    }


    // --- Event Handlers (Fullscreen) ---
    function handleFullscreenChange() {
        // Check which element is fullscreen or if any element is fullscreen
        // This check uses the document, which is correct.
        isFullscreen = !!(document.fullscreenElement ||
                         (document as any).webkitFullscreenElement ||
                         (document as any).mozFullScreenElement ||
                         (document as any).msFullscreenElement);
        console.log('Fullscreen state changed:', isFullscreen);
    }


  // --- Event Handlers (User Interaction) ---

  function togglePlayPause() {
      if (!videoElement) return;
    if (videoElement.paused) {
        console.log('Attempting to play...');
      videoElement.play().catch(error => {
          console.error('Error attempting to play video:', error);
          // Handle play promise rejection (e.g., user hasn't interacted yet, autoplay blocked)
          // You might want to show a "click to play" or "unmute to play" message here
          // Example: show an overlay with a play/unmute button
      });
    } else {
        console.log('Attempting to pause...');
      videoElement.pause();
    }
  }

  function handleSeek(event: Event) {
      if (!videoElement || duration <= 0 || !isFinite(duration)) return;
    const target = event.target as HTMLInputElement;
    const seekPercentage = parseFloat(target.value);
    videoElement.currentTime = (seekPercentage / 100) * duration;
     console.log('Seeking to percentage:', seekPercentage);
  }

  function handleVolumeInput(event: Event) {
      if (!videoElement) return;
    const target = event.target as HTMLInputElement;
    const newVolume = parseFloat(target.value);
    videoElement.volume = newVolume;
    // Setting volume also clears muted state if new volume is > 0
    // State updates happen via the 'volumechange' event listener
  }

  function toggleMute() {
      if (!videoElement) return;
    if (videoElement.muted) {
      videoElement.muted = false;
      // Restore volume to last non-zero value, or default if lastVolume was 0
      // Note: Setting volume un-mutes automatically unless new volume is 0
      videoElement.volume = lastVolume > 0 ? lastVolume : 0.1;
       console.log('Unmuting, restoring volume to:', videoElement.volume);
    } else {
      // Store current volume before muting, if it's not already 0
      if (videoElement.volume > 0) {
        lastVolume = videoElement.volume;
      }
      videoElement.muted = true; // Explicitly mute
       console.log('Muting');
    }
    // State updates happen via the 'volumechange' event listener
  }


  function changeQuality(event: Event) {
    const target = event.target as HTMLSelectElement;
    const level = parseInt(target.value);
    // selectedLevel = level; // This state update happens when hls.js confirms the switch via LEVEL_SWITCHED
    if (hls) {
        hls.currentLevel = level;
        console.log('Attempting to change HLS level to:', level);
    } else {
        console.warn('Manual quality selection not supported (not using HLS.js). Reverting selection.');
        selectedLevel = -1; // Ensure state reflects auto/default for native
         // Re-select the Auto option in the UI if it exists
         // This requires finding the option with value -1 and setting it
         // Or just rely on bind:value = selectedLevel to update the UI
    }
  }

   function toggleFullscreen() {
       // Request fullscreen on the container element
       if (!videoContainerElement) {
           console.warn('Fullscreen container element not found.');
           return;
       }

       const fullscreenApi = document.fullscreenElement ||
                             (document as any).webkitFullscreenElement ||
                             (document as any).mozFullScreenElement ||
                             (document as any).msFullscreenElement;

       if (!fullscreenApi) {
           // Enter fullscreen
           console.log('Entering fullscreen...');
           if (videoContainerElement.requestFullscreen) {
               videoContainerElement.requestFullscreen().catch(e => console.error("Fullscreen enter failed:", e));
           } else if ((videoContainerElement as any).mozRequestFullScreen) {
               (videoContainerElement as any).mozRequestFullScreen().catch((e: any) => console.error("Fullscreen enter failed:", e));
           } else if ((videoContainerElement as any).webkitRequestFullscreen) {
               (videoContainerElement as any).webkitRequestFullscreen().catch((e: any) => console.error("Fullscreen enter failed:", e));
           } else if ((videoContainerElement as any).msRequestFullscreen) {
               (videoContainerElement as any).msRequestFullscreen().catch((e: any) => console.error("Fullscreen enter failed:", e));
           } else {
               console.warn('Fullscreen API not supported on container element.');
           }
       } else {
           // Exit fullscreen
           console.log('Exiting fullscreen...');
           if (document.exitFullscreen) {
               document.exitFullscreen().catch(e => console.error("Fullscreen exit failed:", e));
           } else if ((document as any).mozCancelFullScreen) {
               (document as any).mozCancelFullScreen().catch((e: any) => console.error("Fullscreen exit failed:", e));
           } else if ((document as any).webkitExitFullscreen) {
               (document as any).webkitExitFullscreen().catch((e: any) => console.error("Fullscreen exit failed:", e));
           } else if ((document as any).msExitFullscreen) {
               (document as any).msExitFullscreen().catch((e: any) => console.error("Fullscreen exit failed:", e));
           }
       }
       // State update happens via the fullscreenchange event listener
   }


  // --- Helper Functions ---

  function formatTime(seconds: number): string {
    if (!isFinite(seconds) || seconds < 0) {
        seconds = 0;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    const formattedSeconds = remainingSeconds < 10 ? '0' + remainingSeconds : remainingSeconds;
    return `${minutes}:${formattedSeconds}`;
  }

  // --- Derived values for UI (using $derived) ---
  // Calculate seek bar percentage
  let seekPercentage = $derived(duration > 0 && isFinite(duration) ? (currentTime / duration) * 100 : 0);

  // Determine Volume icon
  let volumeIcon = $derived(isMuted || volume === 0
    ? `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9.383 3.076A1.018 1.018 0 0110 2v16a1.018 1.018 0 01-.617.924L4.382 17H2a2 2 0 01-2-2V7a2 2 0 012-2h2.382L9.383 3.076zM19.912 1.9A.75.75 0 0120 2.22v15.56a.75.75 0 01-1.479.196l-3.71-2.968a.75.75 0 01.082-1.1l.9-.6a2.25 2.25 0 000-3.182l-.9-.6a.75.75 0 01-.082-1.1l3.71-2.969a.75.75 0 01.998-.196z" clip-rule="evenodd" /></svg>`
    : volume > 0.5
      ? `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M9.383 3.076A1.018 1.018 0 0110 2v16a1.018 1.018 0 01-.617.924L4.382 17H2a2 2 0 01-2-2V7a2 2 0 012-2h2.382L9.383 3.076zM17.293 6.293a1 1 0 011.414 0c1.562 1.563 1.562 4.095 0 5.657a1 1 0 01-1.414-1.414c.781-.781.781-2.047 0-2.828a1 1 0 010-1.414zM19.707 4.293a1 1 0 011.414 0c3.124 3.123 3.124 8.197 0 11.32a1 1 0 01-1.414-1.414c2.343-2.343 2.343-6.14 0-8.486a1 1 0 010-1.414z" /></svg>`
      : `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M9.383 3.076A1.018 1.018 0 0110 2v16a1.018 1.018 0 01-.617.924L4.382 17H2a2 2 0 01-2-2V7a2 2 0 012-2h2.382L9.383 3.076zM15.517 7.672a1 1 0 01.033 1.393c.877.96.877 2.366 0 3.326a1 1 0 01-1.427 1.003c1.626-1.683 1.626-4.373 0-6.056a1 1 0 011.394-.033z" /></svg>`);

    // No need for playPauseIcon derived variable anymore, we use text directly

    // Determine Fullscreen icon
    let fullscreenIcon = $derived(isFullscreen
      ? `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H5.414L10 9.586 14.586 5H11a1 1 0 110-2h4a1 1 0 011 1v4a1 1 0 11-2 0V5.414L10.414 10 15 14.586V11a1 1 0 112 0v4a1 1 0 01-1 1h-4a1 1 0 110-2h2.586L10 10.414 5.414 15H9a1 1 0 110 2H5a1 1 0 01-1-1v-4a1 1 0 112 0v2.586L9.586 10 5 5.414V9a1 1 0 01-2 0V5a1 1 0 011-1z" clip-rule="evenodd" /></svg>`
      : `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h4a1 1 0 110 2H5.414L9 9.586V13a1 1 0 11-2 0V10.414L3.414 7H7a1 1 0 110 2H3a1 1 0 01-1-1V5zm14 0a1 1 0 01-1-1h-4a1 1 0 110 2h2.586L11 9.586V13a1 1 0 11-2 0V10.414L16.586 7H13a1 1 0 110 2h4a1 1 0 011-1V5z" clip-rule="evenodd" /></svg>`);

</script>

<style>
  /* Container for video and controls */
  .video-container {
      width: 100%;
      max-width: 800px; /* Or whatever max width you want */
      margin: 0 auto; /* Center the container */
      position: relative; /* Needed for future overlays like buffering indicator */
      display: flex;
      flex-direction: column;
      background-color: #000; /* Black background behind video */
      /* Ensure container can go fullscreen */
      overflow: hidden; /* Hide anything that goes outside */
  }

  /* Styles for when the container is in fullscreen mode */
  /* Note: These styles apply to the element that is fullscreen */
  :fullscreen,
  :-webkit-full-screen,
  :-moz-full-screen,
  :-ms-fullscreen {
      width: 100% !important; /* Take up full screen width */
      max-width: 100% !important; /* Override max-width */
      height: 100% !important; /* Take up full screen height */
      margin: 0 !important; /* Remove centering margin */
      background-color: #000; /* Ensure black background fills screen */
      display: flex; /* Maintain flex layout for video/controls */
      flex-direction: column;
      justify-content: center; /* Center content in the middle */
      align-items: center;
  }


  video {
    width: 100%;
    height: auto; /* Maintain aspect ratio */
    display: block; /* Remove extra space below video element */
    cursor: pointer; /* Indicate it's clickable to play/pause */
    /* In fullscreen, let video fill height while maintaining aspect ratio */
    :fullscreen &,
    :-webkit-full-screen &,
    :-moz-full-screen &,
    :-ms-fullscreen & {
        width: 100%;
        height: 100%;
        object-fit: contain; /* Ensure aspect ratio is maintained */
         cursor: none; /* Hide cursor when idle in fullscreen */
    }
  }

  /* Basic Controls Styling */
  .controls-bar {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    background-color: #222;
    color: #fff;
    font-family: sans-serif;
    gap: 8px;
    user-select: none;
    flex-wrap: wrap;
    /* Position controls absolutely at the bottom in fullscreen */
     :fullscreen &,
     :-webkit-full-screen &,
     :-moz-full-screen &,
     :-ms-fullscreen & {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(34, 34, 34, 0.8); /* Slightly transparent background */
        z-index: 10; /* Ensure controls are on top */
     }
  }

    .controls-bar button:hover {
        color: #0077cc;
    }

   .controls-bar button:disabled {
       opacity: 0.5;
       cursor: not-allowed;
   }


  /* Seek Bar Styling */
  .seek-bar {
    flex-grow: 1;
    width: 20px;
    height: 8px;
    -webkit-appearance: none;
    appearance: none;
    background: #555;
    outline: none;
    border-radius: 4px;
    cursor: pointer;
    transition: height 0.2s ease;
    background: linear-gradient(to right, #0077cc 0%, #0077cc var(--seek-percentage), #555 var(--seek-percentage), #555 100%);
  }

  .seek-bar:hover {
      height: 10px;
  }

  /* Seek Bar Thumb (Handle) */
  .seek-bar::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 10px;
    height: 10px;
    background: #0077cc;
    border: 2px solid #fff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 2px rgba(0,0,0,0.5);
    margin-top: -3.5px;
    opacity: 0;
    transition: opacity 0.2s ease, transform 0.2s ease;
  }

   .seek-bar:hover::-webkit-slider-thumb {
       opacity: 1;
       transform: scale(1.1);
   }


  .seek-bar::-moz-range-thumb {
    width: 15px;
    height: 15px;
    background: #0077cc;
    border: 2px solid #fff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 2px rgba(0,0,0,0.5);
    opacity: 0;
    transition: opacity 0.2s ease, transform 0.2s ease;
  }

   .seek-bar:hover::-moz-range-thumb {
       opacity: 1;
       transform: scale(1.1);
   }





  /* Quality Select */
   .quality-select {
       -webkit-appearance: none;
       -moz-appearance: none;
       appearance: none;
       background-repeat: no-repeat;
       background-position: right 8px center;
       background-size: 12px auto;

       margin-left: 10px;
       background-color: #444;
       color: #fff;
       border: none;
       border-radius: 4px;
       padding: 2px;
       cursor: pointer;
       font-size: 0.8em;
       outline: none;
       transition: background-color 0.2s ease;
       min-width: 60px; /* Give quality select minimum width */
       background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="%23ffffff" height="24" viewBox="0 0 24 24" width="24"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
   }

   .quality-select:hover {
       background-color: #555;
   }

   .quality-select:focus {
       box-shadow: 0 0 0 2px rgba(0, 119, 204, 0.5);
   }

   .quality-select option {
       background-color: #444;
       color: #fff;
   }



  /* Utility for hiding elements visually but keeping them for screen readers */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

</style>

<div role="button" tabindex="0" class="video-container" bind:this={videoContainerElement} ondblclick={toggleFullscreen}>

    <video bind:this={videoElement} playsinline autoplay muted poster="/path-to-your-poster-image.jpg"></video>

    <div class="controls-bar">

        <button onclick={togglePlayPause} aria-label={isPlaying ? 'Pause' : 'Play'}>

            {#if isPlaying}
              <PauseFill class="size-4" />
            {:else}
              <PlayFill class="size-4" />
            {/if}

        </button>

        <div class="text-xs md:text-sm whitespace-nowrap">{formatTime(currentTime)}</div>

        <!-- Seek Bar -->
        <input
            type="range"
            class="seek-bar"
            min="0"
            max="100"
            step="1"
            value={seekPercentage}
            oninput={handleSeek}
            disabled={duration <= 0 || !isFinite(duration)}
            style="--seek-percentage: {seekPercentage}%;"
            aria-label="Seek video"
        />

        <!-- Total Duration -->
        {#if duration > 0 && isFinite(duration)}
             <div class="text-xs md:text-sm whitespace-nowrap">{formatTime(duration)}</div>
        {/if}


        <!-- Quality Select -->
        <!-- Only show quality select if levels are available AND we are using HLS.js (not native HLS, which doesn't support manual change) -->
        {#if qualityLevels.length > 0 && hls}
          <select
            class="quality-select"
            onchange={changeQuality}
            bind:value={selectedLevel}
            aria-label="Select video quality"
            >
            {#each qualityLevels as level}
              <option value={level.index}>{level.label}</option>
            {/each}
          </select>
        {/if}

        <!-- Fullscreen Button (still uses icon) -->
        <button onclick={toggleFullscreen} aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}>
             <ArrowsFullscreen class="w-3 h-3!" />
        </button>

    </div>
</div>