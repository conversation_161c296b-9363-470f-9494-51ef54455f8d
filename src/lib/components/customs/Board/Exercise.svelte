<script lang="ts">
    import { Button } from "$lib/components/ui/button";
    import { Disc } from "svelte-bootstrap-icons";
	import Card from "$lib/components/ui/card/card.svelte";
	import { current_workout_id, exerciseDetails, showExerciseDetailsPage, showMoveExerciseMenuItem, videoToken } from "$lib/stores/generalStore";
    import { onMount } from "svelte";
    import { current_workout_exercise_id } from '$lib/stores/generalStore';
	import type { IExercise } from "$lib/typings";


    let {
        item, onClick, workout_id
    } = $props<{
        item: IExercise,
        workout_id?: number,
        onClick: () => void
    }>();


    // Reactive variable for width
    let dynamicWidth = $state('w-full');

    let isMovingEDetailsPage = $state(false);


    function updateWidth() {
        const screenWidth = window.innerWidth;

        if (screenWidth < 412 && screenWidth > 390 || screenWidth == 390) {
            dynamicWidth = 'max-w-[270px]';
        } else if (screenWidth < 390 ) {
            dynamicWidth = 'max-w-[230px]'; 
        } else {
            dynamicWidth = 'max-w-3/5';
        }
    }

    onMount(() => {
        updateWidth();
        window.addEventListener('resize', updateWidth);

        return () => {
            window.removeEventListener('resize', updateWidth);
        };
    });


</script>


<Card class="w-full h-[95.4px] xs:h-[106px] flex gap-1 mb-1 rounded-none shadow-md bg-white" on:click={async() => {
    current_workout_exercise_id.set(item.data_id);
    current_workout_id.set(workout_id);

    isMovingEDetailsPage = true;
    const response = await fetch(`/api/fetch/${item.exercise_id}`);
    const {exercises, token} = await response.json();
    isMovingEDetailsPage = false;
   
    videoToken.set(token);
    exerciseDetails.set(exercises);
    showExerciseDetailsPage.set(true);
    showMoveExerciseMenuItem.set(true);
 }}>
    <div class="w-[108px] xs:w-[120px] h-[95.4px] xs:h-[106px] pl-1 pb-2 pt-2 pr-2">
        <img src={item.img} alt="Exercise Image" class="w-full h-full object-contain min-w-[106px] xs:min-w-[120px]" />
    </div>
   
    <div class="flex-grow flex flex-col justify-between text-left pb-0 pl-1">
        <h3 class={`line-clamp-2 ${dynamicWidth}; text-[18px] xs:text-[20px] font-semibold overflow-hidden" style="white-space: normal; pr-2`}>
            {item.name}
        </h3>

        <div class="flex-grow"></div>
       
        {#if item.color && item.color.length > 0}
        <div class="w-full h-1 flex pr-[7px]">
          {#each item.color as color, i (i)}
            <div 
              class="h-[2px] flex-1" 
              style="background-color: {color};"
            />
          {/each}
        </div>
      {/if}
        <div class="flex items-bottom justify-between w-full pt-2 pb-1 pr-1">
            <div class="flex gap-1 items-center">
                <img src="{item.icon}" alt="" class="size-[18px] xs:size-[20px]" />
                <span class="font-size: 19px; text-md italic max-w-[200px] truncate">{item.cat}</span>
            </div>
            {#if !isMovingEDetailsPage}
                <Button
                    variant="ghost"
                    size="icon"
                    class="bg-transparent text-balck py-0 px-0 rounded-none size-[21.6px] xs:size-[24px]"
                    on:click={($event) => {
                        $event.stopPropagation();
                        onClick();
                    }}>
                    <div class="mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-[14px] xs:size-[20px] pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 20L20 4M4 4l16 16" />
                    </svg>
                    </div>
                </Button>
            {:else}
                <Disc class="animate-spin duration-300 size-4 mr-1"/>
            {/if}
        </div>
    </div>
 </Card>
 
