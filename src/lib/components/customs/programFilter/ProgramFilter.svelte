<script lang="ts">
	import Button from "$lib/components/ui/button/button.svelte";
	import { SwitchIcon } from "$lib/components/ui/switchIcon";
import type { IProgram } from "$lib/typings";
	import moment from "moment";
	import { onMount } from "svelte";
	import { ArrowCounterclockwise, Search, X } from "svelte-bootstrap-icons";
	import type { Writable } from "svelte/store";


    let { 
      searchText,
      isProgramFilterOpen = $bindable(false),
      dateFilterType,
      sortCriteria,
      sortOrder,
      startDate,
      endDate,
      onFilter,
    } = $props<{ 
        searchText: string,
        isProgramFilterOpen?: boolean
        dateFilterType: string,
        sortCriteria: string,
        sortOrder: string,
        startDate: string,
        endDate: string,
        onFilter: (data: any) => void
    }>();

    let isFill: boolean = $state(false);

    let needResetFilter = $state(false);

    const resetFilter = () => {
      searchText = '';
      startDate = '';
      endDate = '';
      dateFilterType = 'none';
      sortCriteria = '';
      sortOrder = 'dsc';
      isFill = false;

      needResetFilter = false;
  

      onFilter({searchText, dateFilterType, sortCriteria, sortOrder, startDate, endDate});
    }

    const onSearch = () => {
      onFilter({searchText, dateFilterType, sortCriteria, sortOrder, startDate, endDate});
    }

    onMount(() => {
      needResetFilter = searchText !== '' || dateFilterType !== 'none' || sortCriteria !== '' || sortOrder !== 'dsc' || startDate !== '' || endDate !== '';
      console.log('needResetFilter', needResetFilter);
    })


</script>

<div>
    <div class="bg-[#323DA3] py-2 flex justify-center items-center relative">
        <h3 class="text-white text-[20px] font-semibold self-center">
          Search
          
        </h3>

      <Button on:click={() => { isProgramFilterOpen = false; console.log('od') }} class="cursor-pointer absolute right-2 border-none m-0 p-0 bg-transparent">
        <X class="text-white h-8 w-8 self-end font-semibold " />
      </Button>        
    </div>
    <div class="p-4">
        <input type="text" id="search-input" placeholder="Search by name or automated name" 
        onkeyup={() => { isFill = true }} bind:value={searchText}  class="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]" />
    </div>
    <div class="bg-[#323DA3] py-2 flex justify-center items-center">
        <h3 class="text-white text-[20px] font-semibold">Filter</h3>
    </div>
    <div class="p-2 space-y-6">
        <div class="space-y-4">

          
          <div class="gap-4">
            <div class="flex items-center justify-between mt-2">
                <label for="date-filter" class="text-md font-bold text-gray-700 mb-1">Date Filter:</label>
                <select id="date-filter" bind:value={dateFilterType} onchange={() => {isFill = true;}} class="w-[50%] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]">
                    <option value="none">None</option>
                    <option value="last3months">Last 3 months</option>
                    <option value="last6months">Last 6 months</option>
                    <option value="last12months">Last 12 months</option>
                    <option value="after">After</option>
                    <option value="before">Before</option>
                    <option value="between">Between</option>
                </select>
            </div>
          </div>
          
          {#if dateFilterType !== 'none'}
            <div class="space-y-4">
              {#if dateFilterType === 'after' || dateFilterType === 'between'}
                <div class="flex items-center justify-between">
                  <label for="start-date" class="text-md font-bold text-gray-700 mb-1">
                    {dateFilterType === 'after' ? 'After Date:' : 'Start Date:'}
                  </label>
                  <input type="date" id="start-date" bind:value={startDate} onchange={() => {isFill = true}} class="w-[50%] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]" />
                </div>
              {/if}
              {#if dateFilterType === 'before' || dateFilterType === 'between'}
                <div class="flex items-center justify-between">
                  <label for="end-date" class="text-md font-bold text-gray-700 mb-1">
                    {dateFilterType === 'before' ? 'Before Date:' : 'End Date:'}
                  </label>
                  <input type="date" id="end-date" bind:value={endDate} onchange={() => {isFill = true}} class="w-[50%] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]" />
                </div>
              {/if}
            </div>
          {/if}
          
          
          <div class="flex items-center justify-between">
            <label for="sort" class="text-md font-bold text-gray-700 mr-3">Sort by:</label>
                <SwitchIcon checked={sortOrder === 'asc'} on:click={(e) => {
                   sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
                  //  sortPrograms();
                   isFill = true;
                }} />
             <div class="flex h-[40px] w-[50%]">
                <select id="sort" bind:value={sortCriteria} onchange={() => {
                    // sortPrograms();
                    isFill = true;
                }} class="flex-grow px-3  border border-gray-300 rounded-md shadow-sm focus:ring-[#FC570C] focus:border-[#FC570C]">
                    <option value="">None</option>
                    <option value="name">Sort by Name</option>
                    <option value="automatedName">Sort by Automated Name</option>
                    <option value="creationDate">Sort by Date</option>
                  </select>
             </div>
 
          </div>
        </div>
      </div>

      <div class="flex justify-center items-center gap-5 p-2 mt-8">
        <Button class="h-[56px] w-[190px] rounded-full shadow-none bg-[#FD570D] disabled:bg-[#A5A6AA] gap-1" disabled={!isFill && !needResetFilter} on:click={resetFilter}>
            <ArrowCounterclockwise class="size-4 text-white" /> 
            Reset
        </Button>
        <Button class="h-[56px] w-[190px] rounded-full  bg-[#FD570D] disabled:bg-[#A5A6AA] shadow-none gap-1" disabled={!isFill} on:click={() => { onSearch(); isProgramFilterOpen = false }}>
            <Search class="size-4 text-white" /> 
            Search
        </Button>
      </div>
</div>