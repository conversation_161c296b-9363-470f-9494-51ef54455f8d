<script lang="ts">
	import { Label } from '$lib/components/ui/label';
	import { Button } from '$lib/components/ui/button/index.js';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Select from '$lib/components/ui/select';
	import SearchFilter from './SearchFilter.svelte';
	import BrowseContainer from '../Browse/Browse-container.svelte';
	import { onMount } from 'svelte';
	import { ChevronLeft, ChevronDoubleLeft } from 'svelte-bootstrap-icons';
	import ExerciseCard from './Result.svelte';
	import DragulaCopyOnDrag from '../Browse/DragulaCopyOnDrag.svelte';
	import { toast } from 'svelte-sonner';
	import { helpsUrl, selectedProgram, selectedWorkout } from '$lib/stores/generalStore';
	import VideoPlayer from '../VideoPlayer/VideoPlayer.svelte';
	import { derived } from 'svelte/store';
	import { page } from '$app/stores';


	let { 
		isPremium, tagsStore, 
		isFilterOpen = $bindable(false), isSearchOpen = $bindable(true), 
		results = $bindable([]),
		searchValue = $bindable(''),
		path = $bindable([]), currentData = $bindable([]), exercises = $bindable([]), 
		workoutsStore, programsStore
	} = $props();
	let selectedUtilities: string = $state("None");
	let selectedMovement: string  = $state("None");
	let selectedLateral: string  = $state("None");
	let selectedMuscle: string = $state("None");
	let selectedMechanics: string = $state("None");
	let selectedForce: string = $state("None");
	let selectedApparatus: string = $state("None");
	let selectedTag: { name: string; id: number } | null = $state(null);
	let difficulty: { low: number; high: number } = $state({ low: 0, high: 10 });
	let tagsData: any[] = $state([]);
	let isMobile = $state(false);
	let isTablet = $state(false);
	let dialogOpen: boolean = $state(false);
	let localSelectedProgram: any = $state(null);
	let localSelectedWorkout: any = $state(null);
	let workouts: any[] = $state([]);
	let selectedExercise:
		| {
				name: string;
				id: string;
				img: string;
				cat: string;
				icon: string;
				position: string;
				url: string;
				invisible?: boolean;			
				video_src?: string;
				Larg_Img_1?: string;
		  }
		| undefined = $state(undefined);
	let isVideoPlayerReady = $state(false);

	onMount(() => {
     helpsUrl.set('https://exrx.net/WorkoutWebApp/SearchFilter');

		tagsStore.subscribe((value: any) => {
			tagsData = value;
		});
		isMobile = window.outerWidth <= 575;
		isTablet = window.outerWidth > 575 && window.outerWidth < 1024;
	});

	$effect(() => {
		if ($selectedProgram && $selectedProgram.value) {
			localSelectedProgram = $selectedProgram;
		}
	});

	$effect(() => {
		if ($selectedWorkout && $selectedWorkout.value) {
			localSelectedWorkout = $selectedWorkout;
		}
	});

	function handleAddExerciseDialogOpen(exercise: any): void {
		dialogOpen = true;
		selectedExercise = {
			icon: exercise.icon,
			cat: exercise.cat,
			name: exercise.name,
			id: exercise.exercise_id,
			img: exercise.img,
			url: exercise.url,
			invisible: exercise.invisible,
			video_src: exercise.video_src,
			Larg_Img_1: exercise.Larg_Img_1,
			position: '0'
		};
	}

	async function fetchWorkouts(programId: string) {
		try {
			const response = await fetch(`/api/workouts?program=${programId}`);
			const data = await response.json();
			workouts = data.data;
			return data;
		} catch (error) {
			console.error('Error fetching data:', error);
		}
	}


	    const urlProgramId = derived(page, ($page) => {
        const segments = $page.url.pathname.split('/');
        return segments[segments.length - 1];
    });
	    const isSameAsUrl = derived(
        [selectedProgram, urlProgramId],
        ([$selectedProgram, $urlProgramId]) =>
            $selectedProgram?.value?.toString() === $urlProgramId
    );

	async function handleAddExerciseFn() {
		const body = {
			exercise: {
				id: selectedExercise?.id,
				name: selectedExercise?.name,
				cat: selectedExercise?.cat,
				img: selectedExercise?.img,
				icon: selectedExercise?.icon,
				invisible: selectedExercise?.invisible,
				video_src: selectedExercise?.video_src,
				Larg_Img_1: selectedExercise?.Larg_Img_1,
			},
			position: 0,
			workout_id: $selectedWorkout?.value
		};

		const response = await fetch('/api/workouts_exercises', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ items: [body] })
		});

		dialogOpen = false;

		const data = await response.json();



		if (response.ok) {
			if ($isSameAsUrl) {
				console.log('is same as url');
				const res = await fetch(`/api/programs/${$selectedProgram!.value}`);
				const data = await res.json();
				await console.log("Did we get the data?", data);

				workoutsStore.set(data.data.workouts);
			}

			toast.success('Exercise added successfully');

		} else {
			toast.error(data.message || 'Failed to add exercise');
		}
	}
</script>

<section class="flex flex-col gap-2 overflow-y-auto">
	{#if results.length <= 0 && isSearchOpen }
		<div class="flex-1 gap-2 text-center bg-[#323DA3] text-white py-2 px-4 mt-3 w-full">
			<Label class="text-lg">Search & Filter</Label>
		</div>
	{/if}
	<SearchFilter
		bind:searchValue
		bind:isFilterOpen
		bind:isSearchOpen
		bind:selectedUtilities
		bind:selectedMovement
		bind:selectedLateral
		bind:selectedMuscle
		bind:selectedMechanics
		bind:selectedForce
		bind:selectedApparatus
		bind:results
		bind:tagsData
		bind:selectedTag
		bind:isPremium
	/>

	{#if !isFilterOpen}
		<div class="flex-1 text-center bg-[#323DA3] text-white py-2 px-4 w-full">
			<Label class="text-lg">Browse</Label>
		</div>
		<BrowseContainer {programsStore} {workoutsStore} {isPremium} 
		bind:isSearchOpen bind:path bind:currentData bind:exercises/>
	{/if}

	{#if results.length > 0 && isFilterOpen}
		<div class="flex-1 text-center bg-[#323DA3] text-white py-2 px-4 w-full">
			<Label>Search Results</Label>
		</div>
		{#if results.length >= 30}
			<div class="w-full flex items-center justify-center">
				<div class="flex flex-col items-center justify-center text-center">
					<p>Too many exercises found.</p>
					<p>Please be more specific in your search or apply additional filters.</p>
				</div>
			</div>
		{:else}
			{#if isMobile}
				{#each results as result}
					<ExerciseCard exercise={result} onClick={() => handleAddExerciseDialogOpen(result)} />
				{/each}
			{:else}
				<DragulaCopyOnDrag items={results} onClick={handleAddExerciseDialogOpen} />
			{/if}
			{#if !isPremium}
                        <div class="flex flex-row items-center justify-center text-center p-[8px] gap-[8px] mt-2 bg-white">
                            <p class="flex-1">Double your exercise library ad-free</p>
                            <Button variant="outline" class="bg-[#FC570C] text-white flex-1 py-6 rounded-lg z-10"
							on:click={() => window.open('https://exrx.net/Premium', '_blank', 'noopener,noreferrer')}>
							Upgrade to Premium
						</Button>
                        </div>
			{/if}
			<Dialog.Root open={dialogOpen} onOpenChange={(val) => (dialogOpen = val)}>
				<Dialog.Content class="max-w-lg w-[95vw] mx-auto rounded-md">
					<Dialog.Header>
						<Dialog.Title class="my-1 text-center text-[25px] font-semibold">{selectedExercise?.name}</Dialog.Title>
						<!-- <Dialog.Title class="mb-3">Add To Workout</Dialog.Title> -->
						<Dialog.Description>
							<div class="flex flex-col gap-2 mb-3">
								<VideoPlayer
									src={selectedExercise?.video_src}
									poster={selectedExercise?.Larg_Img_1}
							/>
							    <label class="text-center text-black text-[19px] m-3" for="program">Select a Program</label>
								<Select.Root
									selected={localSelectedProgram}
									onSelectedChange={(val: any) => {
										fetchWorkouts(val?.value);
										$selectedProgram = val;
									}}
								>
									<Select.Trigger class="w-full">
										<Select.Value class="text-[16px] text-black" placeholder="Select a Program" />
									</Select.Trigger>
									<Select.Content>
										{#each $programsStore as program}
											<Select.Item value={program.id}
												>{program.name ? program.name : program.automated_name}</Select.Item
											>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>
							<div class="flex flex-col gap-2 mb-6">
                			<label class="text-center text-black text-[18px] m-3" for="workout">Select a Workout</label>
								<Select.Root
									selected={localSelectedWorkout}
									onSelectedChange={(val: any) => {
										selectedWorkout.set(val);
									}}
								>
									<Select.Trigger class="w-full">
										<Select.Value class="text-[16px] my-5 text-black" placeholder="Select a Workout" />
									</Select.Trigger>
									<Select.Content>
										{#if workouts.length <= 0}
											<Select.Item value={null}>No workouts available</Select.Item>
										{:else}
											<Select.Item value={null}>Select a workout</Select.Item>
										{/if}
										{#each workouts as workout}
											<Select.Item value={workout.id}
												>{workout.name ? workout.name : workout.automated_name}</Select.Item
											>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>
							 <div class="flex justify-center">
							<Button class="bg-orange-600 m-auto text-white" on:click={handleAddExerciseFn}>Add Exercise to Workout</Button>
							</div>
						</Dialog.Description>
					</Dialog.Header>
				</Dialog.Content>
			</Dialog.Root>
		{/if}
	{/if}
</section>
