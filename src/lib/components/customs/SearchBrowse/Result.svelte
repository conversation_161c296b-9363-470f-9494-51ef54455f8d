<script lang="ts">
	import { Button } from "$lib/components/ui/button";
	import Card from "$lib/components/ui/card/card.svelte";
    import { onMount } from "svelte";
	import { Plus, Disc } from "svelte-bootstrap-icons";
  import { showExerciseDetailsPage, exerciseDetails, showAddExerciseMenuItem, videoToken } from "$lib/stores/generalStore";

    export let exercise: any
    export let onClick: () => void = () => {};

    // Reactive variable for width
    let dynamicWidth = 'w-full';

    let isMovingEDetailsPage = false;

    function updateWidth() {
        const screenWidth = window.innerWidth;

        if (screenWidth < 412 && screenWidth > 390 || screenWidth == 390) {
            dynamicWidth = 'max-w-[270px]';
        } else if (screenWidth < 390 ) {
            dynamicWidth = 'max-w-[230px]'; 
        } else {
            dynamicWidth = 'max-w-3/5';
        }
    }

    onMount(() => {
        updateWidth();
        window.addEventListener('resize', updateWidth);

        return () => {
            window.removeEventListener('resize', updateWidth);
        };
    });
</script>


<Card class="w-full h-[106px] flex gap-1 mb-1 rounded-none shadow-md bg-white"on:click={async() => {
  isMovingEDetailsPage = true;
  const response = await fetch(`/api/fetch/${exercise.id}`);
  const {exercises, token} = await response.json();
  isMovingEDetailsPage = false;
 
  videoToken.set(token);
  exerciseDetails.set(exercises);
  showAddExerciseMenuItem.set(true);
  showExerciseDetailsPage.set(true);

}}>
    <div class="w-[120px] h-[106px] pl-1 pb-2 pt-2 pr-2">
        <img src={exercise.img} alt="Exercise Image" class="w-full h-full object-fit: contain; min-w-[120px]" />
    </div>
    <div class="flex-grow flex flex-col justify-between text-left pb-0 pl-1">
      <h3 class={`line-clamp-2 ${dynamicWidth}; text-[20px]  font-semibold overflow-hidden" style="white-space: normal; pr-2`}>
          {exercise.name}
      </h3>

      <div class="flex-grow"></div>
       
      <div>
        {#if exercise.color && exercise.color.length > 0}
        <div class="w-full h-1 flex pr-[5px]">
          {#each exercise.color as color, i (i)}
            <div 
              class="h-[2px] flex-1" 
              style="background-color: {color};"
            />
          {/each}
        </div>
      {/if}
      
      <div class="flex items-end justify-between w-full pt-2 pb-1">
        <div class="flex gap-1 items-center">
          <img src="{exercise.icon}" alt="" class="w-4 h-4" />
          <span class="text max-w-[200px] truncate italic">{exercise.cat}</span>
        </div>
        {#if !isMovingEDetailsPage}
          <Button
            variant="ghost"
            size="icon"
            class="bg-transparent text-black py-0 px-0 rounded-none w-4 h-4 md:mr-1 hover:bg-transparent"
            on:click={($event) => {
              $event.stopPropagation();
              onClick();
          }}>
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M12 4v16M4 12h16" />
            </svg>
          </Button>
        {:else}
          <Disc class="animate-spin duration-300 size-4 mr-1"/>
        {/if}
      </div>                                    
    
  </Card>