<script lang="ts">
    import { flip } from 'svelte/animate';
    import { dndzone, TRIGGERS } from 'svelte-dnd-action';
	import { onMount } from 'svelte';
	import { writable } from 'svelte/store';
	import ExercisesCard from './Exercises-card.svelte';

    interface IProps {
        items: any[]
    }

    let  { items } : IProps = $props();

    const exercises = writable<any []>()

    exercises.set(items);
   

    const flipDurationMs = 300;
    function handleDndConsider(e: any) {
        // console.warn(`got consider ${JSON.stringify(e.detail, null, 2)}`);
        const {trigger, id} = e.detail.info;
        if (trigger === TRIGGERS.DRAG_STARTED) {
            console.warn(`copying ${id}`);
            const idx = items.findIndex(item => item.id === id);
            const newId = `${id}_copy_${Math.round(Math.random()*100000)}`;
            e.detail.items.splice(idx, 0, {...items[idx], id: newId});
        }
        items = e.detail.items;
    }
    function handleDndFinalize(e: any) {
        // console.warn(`got finalize ${JSON.stringify(e.detail, null, 2)}`);
        // items = e.detail.items;
    }
</script>

<section use:dndzone={{items, flipDurationMs}} on:consider={handleDndConsider} on:finalize={handleDndFinalize}>
    {#each items as item(item.id)}
        <div animate:flip="{{duration: flipDurationMs}}" class="my-3">
            <ExercisesCard exercise={item} />
        </div>
    {/each}
</section>