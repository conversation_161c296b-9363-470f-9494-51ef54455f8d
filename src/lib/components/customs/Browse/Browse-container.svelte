<script lang="ts">
    import * as Dialog from "$lib/components/ui/dialog";
    import * as Select from "$lib/components/ui/select";
    import jsonData from './categorieswithids.json'
    import { onMount } from 'svelte';
	import ExercisesCard from './Exercises-card.svelte';
	import Button from "$lib/components/ui/button/button.svelte";
	import { ChevronLeft, ChevronRight, Disc } from "svelte-bootstrap-icons";
	import DragulaCopyOnDrag from "./DragulaCopyOnDrag.svelte";
	import { toast } from "svelte-sonner";
	import type { Writable } from "svelte/store";
    import { resolveTransformedExercises } from "$lib/utils/tags_exercise.utils";
    import { ScrollArea } from "$lib/components/ui/scroll-area/index.js";
	import { get } from "svelte/store";
    import { derived } from "svelte/store";
    import { page } from "$app/stores";

    import { defaultProgram, selectedWorkout, selectedProgram } from "$lib/stores/generalStore";
    import { helpsUrl, exerciseDetails } from "$lib/stores/generalStore";
    import VideoPlayer from '../VideoPlayer/VideoPlayer.svelte';
	import type { IWorkout } from "$lib/typings";

    interface IProgram {
        id: string,
        name: string,
        automated_name: string,
        workouts?: any[]
    }

    interface IProps {
        programsStore: Writable<IProgram[]>,
        workoutsStore: Writable<IWorkout[]>,
        isPremium: boolean,
        isSearchOpen?: boolean,
        path?: string[],
        currentData?: any[],
        exercises?: any[]
    }

    let { programsStore, workoutsStore, isPremium, isSearchOpen = $bindable(true), 
        path = $bindable([]), currentData = $bindable([]), exercises = $bindable([]) }: IProps = $props();

    let workouts: any[] = $state([]);

    let isLoading: boolean = $state(false);

    let selectedExercise: {
        name: string;
        id: string;
        img: string;
        cat: string;
        icon: string;
        position: string;
        url: string;
        invisible: boolean;
        video_src: string;
        Larg_Img_1?: string;
    } | undefined = $state(undefined);
    let isVideoPlayerReady = $state(false);

    let isMobileScreen: boolean = $state(false);
    let isTabletScreen: boolean = $state(false);

    

    let selectedProgramFromStore: { value: string; label: string } | null = $state(null);
    let selectedWorkoutFromStore: { value: string; label: string } | null = $state(null);

    $effect(() => {
        
        if ($selectedProgram && $selectedProgram.value) {
            selectedProgramFromStore = $selectedProgram;
            fetchWorkouts($selectedProgram.value)
            
        }
    });

    // $effect(() => {
    //     if ($selectedWorkout && $selectedWorkout.value) {
    //         selectedWorkoutFromStore = $selectedWorkout;
    //     }
    // });

     $effect(() => {
        if ($selectedWorkout && $selectedWorkout.value) {
            selectedWorkoutFromStore = $selectedWorkout;
        }
    });

    $effect(() => {
        if (selectedExercise && selectedExercise.video_src) {
            isVideoPlayerReady = false; // Reset when video source changes
        } else if (!selectedExercise) {
            isVideoPlayerReady = true; // No exercise, so no video to wait for
        }
    });
    
    let final = $state(false);
    let currentCatName = $state('');



    onMount(() => {
        if (currentData.length === 0) {
            currentData = Object.entries(jsonData);
        }
        fetchWorkouts(selectedProgram!.value);
        isMobileScreen = window.outerWidth <= 575;
        isTabletScreen = window.outerWidth > 575 && window.outerWidth < 1024;
    });

    $effect(() => {
        if (final) {
            // helpsUrl.set('https://exrx.net/WorkoutTools/ExerciseLists');
            helpsUrl.set('https://exrx.net/WorkoutWebApp/BrowseExercises');
        }
        if (currentData.length === 0) {
            currentData = Object.entries(jsonData);
            final = false;
            currentCatName = '';
        }
    });

     // Handle clicking on a JSON element
    function handleClick(key: string) {
        helpsUrl.set('https://exrx.net/WorkoutTools/Subcategory');
        path.push(key);
        let data = jsonData;
        for (const p of path) {
            currentCatName = data[p].name
            data = data[p].sublevel || data[p];
        }
        currentData = Object.entries(data);
    }

      function handleBack() {
        path.pop();
        if(path.length == 0){
            isSearchOpen = true;
        }
        let data = jsonData;
        for (const p of path) {
            currentCatName = data[p].name;
            data = data[p].sublevel || data[p];
        }
        currentData = Object.entries(data);
        final = false
    }

    async function fetchData(link: string, key: string) {
        isLoading = true;
        final = true;
        try {
            const response = await fetch(`/api/fetch?link=${encodeURIComponent(link)}`);
            const data = await response.json();

            exercises = [];
            console.log('Fetched exercises:', data.exercises);
            
            data.exercises.map((item: any) => {
                exercises.push({
                    id: `${item.Exercise_Id}_temp_${Date.now()}`, 
                    exercise_id: item.Exercise_Id,
                    name: item.Exercise_Name_Complete_Abbreviation,
                    img: item.Small_Img_1,
                    icon: item.Utility_Icon,
                    cat: item.Overall_Category,
                    url: item.URL,
                    invisible: item.invisible == "0" ? false : true,
                    video_src: item.video_src,
                    Larg_Img_1: item.Larg_Img_1,
                })
            });
            if(!isPremium){
                exercises = exercises.filter((exercise) => !exercise.invisible);
            }
            isLoading = false;
            exercises = await resolveTransformedExercises(exercises);
            path.push(key);
            
        } catch (error) {
            isLoading = false
            console.error('Error fetching data:', error);
        } finally {
            isLoading = false;
        }
    }

    async function fetchWorkouts(programId: string) {
        try {
            const response = await fetch(`/api/workouts?program=${programId}`);
            const data = await response.json();
            workouts = data.data;
            if(!$selectedWorkout || $selectedWorkout.value === null || $selectedWorkout.value === undefined){
                selectedWorkout.set({value: data.data[0].id, label: data.data[0].name})
            }

            
            return data;
        } catch (error) {
            console.error('Error fetching data:', error);
        }
    }

    
    const urlProgramId = derived(page, ($page) => {
        const segments = $page.url.pathname.split('/');
        return segments[segments.length - 1];
    });

    const isSameAsUrl = derived(
        [selectedProgram, urlProgramId],
        ([$selectedProgram, $urlProgramId]) =>
            $selectedProgram?.value?.toString() === $urlProgramId
    );



    async function handleAddExercise() {
        console.log('selectedExercise', selectedExercise);
        const body = {
            exercise: {
                id: selectedExercise?.id.split("_")[0],
                name: selectedExercise?.name,
                cat: selectedExercise?.cat,
                img: selectedExercise?.img,
                icon: selectedExercise?.icon,
                invisible: selectedExercise?.invisible,
                url: selectedExercise?.url,
                video_src: selectedExercise?.video_src,
                Larg_Img_1: selectedExercise?.Larg_Img_1,
            },
            position: 0,
            workout_id: get(selectedWorkout)?.value
        };

        const response = await fetch('/api/workouts_exercises', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({items: [body]})
        });

    open = false;

    const data = await response.json();    
    

    if (response.ok) {
        if ($isSameAsUrl) {
            console.log('is same as url');
            const res = await fetch(`/api/programs/${$selectedProgram!.value}`);
            const data = await res.json();
            await console.log("Did we get the data?", data);

            workoutsStore.set(data.data.workouts);
        }

        toast.success('Exercise added successfully');
    } else {
        toast.error(data.message || 'Failed to add exercise');
    }

    
    // store.set(resWorkouts.data);
}

let open = $state(false);

const exerciseAdd = (exercise: any) => {
    open = true;
    selectedExercise = {
        icon: exercise.icon,
        cat: exercise.cat,
        name: exercise.name,
        id: exercise.id,
        img: exercise.img,
        invisible: exercise.invisible,
        position: '0',
        video_src: exercise.video_src,
        Larg_Img_1: exercise.Larg_Img_1,
        url: exercise.url
    };
    console.log('selectedExercise', selectedExercise);
    // selectedProgram.set({ value: programId.toString(), label: store.get()[0].name });
};

</script>

<div class="bg-[#f0f2ff] w-full flex flex-col overflow-hidden">
    {#if path.length > 0}
        <div class="relative flex items-center h-8 mt-1">
            <Button
                variant="link"
                class="text-[#313da3] gap-1 text-sm font-semibold flex items-center hover:no-underline p-0 ml-2 absolute left-0 z-10"
                on:click={handleBack}
            >
                <ChevronLeft class="size-5" />
            </Button>
            <span class="font-semibold px-10 text-left text-lg text-nowrap truncate">{currentCatName}</span>
        </div>
    {/if}
    
    <div class="flex-1 overflow-y-auto">
        <div>
            {#if final}
                {#if isLoading}
                    <div class="h-20 w-full flex justify-center items-center">
                        <Disc class="w-10 h-10 text-gray-400 animate-spin" />
                    </div>
                {:else}
                    {#if isMobileScreen }
                        {#each exercises as exercise}
                            <ExercisesCard exercise={exercise} onClick={() => exerciseAdd(exercise)} />
                        {/each}
                    {:else}
                        <DragulaCopyOnDrag items={exercises} onClick={exerciseAdd} /> 
                    {/if}
                    {#if !isPremium}
                        <div class="flex flex-row items-center justify-center text-center p-[8px] gap-[8px] mt-2 bg-white">
                            <p class="flex-1">Double your exercise library ad-free</p>
                            <Button variant="outline" class="bg-[#FC570C] text-white flex-1 py-6 rounded-lg"
                            on:click={() => window.open('https://exrx.net/Premium', '_blank', 'noopener,noreferrer')}
                            >Upgrade to Premium
                        </Button>
                        </div>
                    {/if}                   
                {/if}
            {:else}
                <ul>
                    {#each currentData as [key, value]}
                        <li class="border bg-gray-50 border-gray-200 my-2 font-semibold">
                            <button class="w-full py-3 px-1 flex justify-between items-center" onclick={() => {
                                isSearchOpen = false;
                                if (value.link === undefined) {
                                    handleClick(key);
                                } else {
                                    fetchData(value.link, key);
                                }
                            }}>
                                <div class="pl-3 text-[18px] text-left w-full">{value.name}</div>
                                <ChevronRight class="size-4 flex-shrink-0" />
                            </button>
                        </li>
                    {/each}
                </ul>
            {/if}
        </div>
    </div>
</div>

<Dialog.Root open={open} onOpenChange={(val) => open = val}>
	<Dialog.Content class="max-w-lg w-[95vw] rounded-md">
        <Dialog.Header>
            <Dialog.Title class="my-1 text-center text-[25px] font-semibold">{selectedExercise?.name}</Dialog.Title>
        </Dialog.Header>
        <Dialog.Description >
            <div class="flex flex-col gap-2 mb-3">
                <VideoPlayer
                        src={selectedExercise?.video_src}
                        poster={selectedExercise?.Larg_Img_1}
                        
                />
                <label class="text-center text-black text-[19px] m-3" for="program">Select a Program</label>

                <Select.Root selected={selectedProgramFromStore!} onSelectedChange={(val: any) => {
                    selectedProgram.set(val)
                }}>
                    <Select.Trigger class="w-full">
                        <Select.Value class="text-[16px] text-black" placeholder="Select a Program" />
                    </Select.Trigger>
                    <Select.Content>
                        {#each $programsStore as program}
                            <Select.Item value={program.id}>{program.name ? program.name : program.automated_name}</Select.Item>
                        {/each}
                    </Select.Content>
                </Select.Root>
            </div>
            <div class="flex flex-col gap-2 mb-6">
                <label class="text-center text-black text-[19px] m-3" for="workout">Select a Workout</label>
                <Select.Root selected={selectedWorkoutFromStore!} onSelectedChange={(val: any) => selectedWorkout.set(val)}>
                <Select.Trigger class="w-full">
                    <Select.Value class="text-[16px] my-5 text-black" placeholder="Select a Workout" />
                </Select.Trigger>
                <Select.Content>
                    {#each workouts as workout}
                        <Select.Item value={workout.id}>{workout.name ? workout.name : workout.automated_name}</Select.Item>
                    {/each}
                </Select.Content>
                </Select.Root>
            </div>
            <div class="flex justify-center">
                <Button class="bg-orange-600 m-auto text-white" on:click={handleAddExercise}>Add Exercise to Workout</Button>
            </div>
        </Dialog.Description>
    </Dialog.Content>
</Dialog.Root>