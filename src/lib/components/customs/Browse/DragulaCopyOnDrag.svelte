<script lang="ts">
    import { flip } from 'svelte/animate';
    import { dndzone, TRIGGERS, SHADOW_ITEM_MARKER_PROPERTY_NAME } from 'svelte-dnd-action';
	import ExercisesCard from './Exercises-card.svelte';
    // import { resolveTransformedExercises } from "$lib/utils/tags_exercise.utils";
	// import { onMount, onDestroy } from 'svelte';
    export let items: any[];
    export let onClick: (exercise: any) => void = () => {}


    const flipDurationMs = 300;
    let shouldIgnoreDndEvents = false;
    function handleDndConsider(e) {

        // console.warn(`got consider ${JSON.stringify(e.detail, null, 2)}`);
        const {trigger, id} = e.detail.info;
        if (trigger === TRIGGERS.DRAG_STARTED) {
            console.warn(`copyings ${id}`);
            const idx = items.findIndex(item => item.id === id);
            const newId = `${id.split('_')[0]}_temp_${Date.now()}}`;
						// the line below was added in order to be compatible with version svelte-dnd-action 0.7.4 and above 
					  e.detail.items = e.detail.items.filter(item => !item[SHADOW_ITEM_MARKER_PROPERTY_NAME]);
            e.detail.items.splice(idx, 0, {...items[idx], id: newId});
            items = e.detail.items;
            shouldIgnoreDndEvents = true;
        }
        else if (!shouldIgnoreDndEvents) {
            items = e.detail.items;
        }
        else {
            items = [...items];
        }
    }
    function transformDraggedElement(draggedEl, data, index) {
        draggedEl.querySelector(".card").style.transform = "rotate(10deg)";
	}
    function handleDndFinalize(e) {
        // console.warn(`got finalize ${JSON.stringify(e.detail, null, 2)}`);
        if (!shouldIgnoreDndEvents) {
            items = e.detail.items;
        }
        else {
            items = [...items];
            shouldIgnoreDndEvents = false;
        }
    }
</script>

<style>
    /* :global(#dnd-action-dragged-el .column) {
		transform: rotate(7deg);
	} */
    section {
        display: flex;
        flex-direction: column;
	}
</style>

<section class="flex fex-col" use:dndzone={{items, dropFromOthersDisabled: true,     flipDurationMs, type: 'workout_items', transformDraggedElement}} on:consider={handleDndConsider} on:finalize={handleDndFinalize}>
    {#each items as item(item.id)}
        <div animate:flip="{{duration: flipDurationMs}}">    
        <ExercisesCard 
            exercise={item} 
            onClick={() => onClick(item)} 
        />
        </div>
    {/each}
</section>