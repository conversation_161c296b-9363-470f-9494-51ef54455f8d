
<script lang="ts">
	import { ArrowDown, ArrowUp, X } from "svelte-bootstrap-icons";

    const sortCriteria: any = {
        automatedName: 'Sort Automated Name',
        creationDate: 'Sort Date',
        name: 'Sort Name'
    }



let { 
    sortIcon = $bindable(''),
      title = $bindable(''),
      
      onRemove,
    } = $props<{ 
        sortIcon: string,
       title: string,
        onRemove: () => void, 
    }>();


</script>

<div class="flex items-center flex-wrap w-auto justify-center rounded-full h-[30px] bg-[#D6D9F8] border border-[#323DA3] text-xm text-[#323DA3] pl-2 text-sm">
    {#if sortIcon === 'desc'}
        <ArrowDown class="size-5" />
    {:else if sortIcon === 'asc'}
        <ArrowUp class="size-5" />
    {/if}

    <span class="text-[15px]" style="letter-spacing: -0.2px;">{sortCriteria[title] || title}</span>
    <button onclick={onRemove}><X class="size-6" /></button>
</div>