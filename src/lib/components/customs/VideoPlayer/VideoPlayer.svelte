<script lang="ts">
	import { videoToken } from "$lib/stores/generalStore";
	import { extractVideoId } from "$lib/utils";
	import { onMount } from "svelte";

	


let {
  src, onclick, poster
} = $props<{
  src?: string;
  poster?: string;
  onclick?: () => void;
}>();

  let videoEl: HTMLVideoElement;

    async function loadVideo() {
      const response = await fetch(`/api/video/${extractVideoId(src)}?token=${$videoToken}`);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      videoEl.loop = true;
      videoEl.src = url;
      await videoEl.play();

      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 3000);
    }


  onMount(() => {
    if (!src) return;
    loadVideo();
  });
</script>

<video 
  bind:this={videoEl}
  poster="{poster}"
  loop autoplay
  playsinline
  onclick={onclick}
  class="w-full"
>
</video>