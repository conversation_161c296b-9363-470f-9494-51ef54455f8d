<script lang="ts">
    import { Button } from "$lib/components/ui/button";
    import Card from "$lib/components/ui/card/card.svelte";
    import { exerciseDetails, showExerciseDetailsPage, videoToken } from "$lib/stores/generalStore";
    import { Plus, Trash, Disc } from "svelte-bootstrap-icons";
	import { toast } from "svelte-sonner";

    export let item;
    export let onClick: () => void = () => {};

    let isMovingEDetailsPage = false;


</script>


<Card 
    class="w-full flex gap-2 mb-2 rounded-none shadow-none" 
    on:click={async() => {

        isMovingEDetailsPage = true;
        const response = await fetch(`/api/fetch/${item.id}`);
        const {exercises, token} = await response.json();
        isMovingEDetailsPage = false;

        if(exercises) {
            videoToken.set(token);
            exerciseDetails.set(exercises)
            showExerciseDetailsPage.set(true)
        } else {
            exerciseDetails.set(null);
            showExerciseDetailsPage.set(true);
        }
    }}
>
    <div class="w-[100px] h-[80px]">
        <img src={item.img} alt="Exercise Image" class="w-full h-full object-cover" />
    </div>
    <div class="grow flex flex-col justify-between p-2">
        <h3 class="text-md truncate">{item.name}</h3>
        <div class="flex gap-2 items-center justify-between w-full">
            <div class="flex gap-1 items-center">
                <img src="{item.icon}" alt="" srcset="">
                <span class="text-sm">{item.cat}</span>
            </div>
            {#if !isMovingEDetailsPage}
                <Button variant="ghost" 
                        class="text-red-700 gap-1 text-sm" 
                        data-no-dnd="true"
                        on:click={($event) => {
                            $event.stopPropagation();
                            onClick();
                        }}>
                    <Trash class="w-4 h-4" />
                </Button>
            {:else}
                <Disc class="animate-spin duration-300 size-4 mr-1"/>
            {/if}
        </div>
    </div>
</Card>