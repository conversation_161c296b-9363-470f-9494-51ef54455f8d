<script lang="ts">
  import * as Card from "$lib/components/ui/card";
  import Button from "$lib/components/ui/button/button.svelte";
  import * as Dialog from "$lib/components/ui/dialog";
  import { Input } from "$lib/components/ui/input";
  import { toast } from "svelte-sonner";
  import { ChevronRight, Trash, X, Disc, PlusCircle, Search } from "svelte-bootstrap-icons";
  import { type Writable } from "svelte/store";
  import type { IProgram } from "$lib/typings";
  import { defaultProgram, selectedProgram, selectedWorkout, deleteProgramMode, newProgramDetails } from "$lib/stores/generalStore";
	import { SwitchIcon } from "$lib/components/ui/switchIcon";
	import ProgramFilter from "../programFilter/ProgramFilter.svelte";
  import Chip from "../chip/Chip.svelte";
	import moment from "moment";
	import { onMount } from "svelte";
  import { goto } from "$app/navigation";


  let { 
    store, isProgramFilterOpen = $bindable(false), isDeleteMode = false,
    changeTab
  } = $props<{ 
    store: Writable<IProgram[]>, 
    isDeleteMode?: boolean,
    isProgramFilterOpen?: boolean,
    changeTab: () => void
  }>();

  let filteredPrograms = $state($store);
  let programId: number | null = $state(null);
  let programName: string | null = $state(null);
  let isFillterLoading: boolean = $state(true);
  let openDeleteDialog: boolean = $state(false);
  let currentProgram: any = $state(null);
  let isLoading: boolean = $state(false);
  let isScrolled: boolean = $state(false);
  let defaultPartOfList: boolean = $state(false);
  let currentSelectedProgram: any = $state(null);

  let isChangingProgram: boolean = $state(false);

  let isFilltered: boolean = $state(false);
  // let openSearchModal: boolean = $state(false);
  let containerRef: HTMLDivElement;

  let searchText: string = $state('');

    let sortCriteria: string = $state('');  
    let sortOrder: string = $state('dsc');  
    let startDate: string = $state(''); 
    let endDate: string = $state(''); 
    let dateFilterType = $state('none');
    let currentDeleteProgramMode: boolean = $state(false);

  const handleScroll = (event: Event) => {
    if (!containerRef) return;
    isScrolled = containerRef.scrollTop > 10;
  };


  $effect(() => {    
    if (containerRef) {
      containerRef.addEventListener('scroll', handleScroll);
      return () => {
        containerRef.removeEventListener('scroll', handleScroll);
      };
    }
  });

  $effect(() => {
    if ($newProgramDetails) {
       programId = Number($newProgramDetails.value);
       programName = $newProgramDetails.label;
      
    }
  })
   

  const handleEditProgramClick = async (name: string, id: string) => {
    const res = await fetch('/api/programs/' + id, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: name
      })
    });

    const data = await res.json();

    if (!data.success) {
      toast.error(data.message);
      return;
    }

    const temp = $store.filter((program: IProgram) => {
      if (program.id === id) {
        program.name = data.data.name;
      }

      return program;
    });

    store.set(temp);
    toast.success(data.message);
  };

  const handleDelete = async () => {
    isLoading = true;
    const res = await fetch('/api/programs/' + currentProgram.id, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = await res.json();

    if (!data.success) {
      toast.error(data.message);
      return;
    }

    const temp = $store.filter((program: IProgram) => program.id !== currentProgram.id);

    if (temp.length === 0) {
      window.location.href = '/';
    }

    store.set(temp);
    toast.success(data.message);
    deleteProgramMode.set(false);
    isLoading = false;
    openDeleteDialog = false;
  };

  const updateFilterStorage = async () => {
    localStorage.setItem('filterParams', JSON.stringify({
      searchText,
      dateFilterType,
      sortCriteria,
      sortOrder,
      startDate,
      endDate
    }));
  }


    const sortPrograms = () => {
      console.log('sortPrograms')
      filteredPrograms.sort((a: IProgram, b: IProgram) => {
        let comparison = 0;
        
        if (sortCriteria === 'name') {
          comparison = (a.name ?? '').localeCompare(b.name ?? '');
        } else if (sortCriteria === 'automatedName') {
          comparison = a.automated_name ? a.automated_name.localeCompare(b.automated_name ? b.automated_name : '') : 1;
        } else if (sortCriteria === 'creationDate') {
          comparison = moment(a.date_updated).diff(moment(b.date_updated));
        }
  
        return sortOrder === 'asc' ? comparison : -comparison;
      });

      // updateFilterStorage();
      
      // filterPrograms(); // Filter after sorting
    };

  // const sortPrograms = () => {


  //   filteredPrograms = filteredPrograms.sort((a: IProgram, b: IProgram) => {
      
    
  //     const comparison = moment(a.date_created).diff(moment(b.date_created));
      

  //     return sortOrder === 'asc' ? comparison : -comparison;
  //   });
  // };

  async function handleAddProgramClick() {
    try {
      isLoading = true;
      const res = await fetch(`/api/programs`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
      });

      const responseData = await res.json();
      if (responseData.success) {
        store.update((programs: IProgram[]) => [...programs, responseData.data]);
        defaultProgram.set(responseData.data);
        selectedProgram.set({ value: responseData.data.id, label: responseData.data.name || responseData.data.automated_name });
        selectedWorkout.set(null); // Set selectedWorkout to null
       resetFilter();
        toast.success(responseData.message);
      } else {
        toast.error(responseData.message);
      }


    } catch (e) {
      toast.error('Failed to add program');
    } finally {
      isLoading = false;
    }
  }

  const resetFilter = () => {
    searchText = '';
    startDate = '';
    endDate = '';
    dateFilterType = 'none';
    sortCriteria = '';
    sortOrder = 'dsc';

    localStorage.setItem('filterParams', JSON.stringify({
          searchText,
          dateFilterType,
          sortCriteria,
          sortOrder,
          startDate,
          endDate
        }));

  }

  const filterPrograms = () => {
      let temp = $store;

      if (searchText) {
        temp = temp.filter((program: IProgram ) =>
          program.name && program.name.toLowerCase().includes(searchText.toLowerCase()) ||
          program.automated_name && program.automated_name.toLowerCase().includes(searchText.toLowerCase())
        );
      }

      switch (dateFilterType) {
        case 'last3months':
          
            temp = temp.filter((program: IProgram) =>
              moment(program.date_updated).isSameOrAfter(moment().subtract(3, 'months'))
            );

          break;
        case 'last6months':
            temp = temp.filter((program: IProgram) =>
              moment(program.date_updated).isSameOrAfter(moment().subtract(6, 'months'))
            );
          break;
        case 'last12months':
            temp = temp.filter((program: IProgram) =>
              moment(program.date_updated).isSameOrAfter(moment().subtract(12, 'months'))
            );
          break;
        case 'after':
          if (startDate) {
            temp = temp.filter((program: IProgram) =>
              moment(program.date_updated).isSameOrAfter(moment(startDate))
            );
          }
          break;
        case 'before':
          if (endDate) {
            temp = temp.filter((program: IProgram) =>
              moment(program.date_updated).isSameOrBefore(moment(endDate))
            );
          }
          break;
        case 'between':
          if (startDate && endDate) {
            temp = temp.filter((program: IProgram) =>
              moment(program.date_updated).isBetween(moment(startDate), moment(endDate), null, '[]')
            );
          }
          break;
      }

      // Move selected program to the top of the list if it is part of the filtered programs
      temp.forEach((program, index) => {
        if (program.id === $selectedProgram?.value) {
          currentSelectedProgram = program.id;
          const [selected] = temp.splice(index, 1);
          temp.unshift(selected);
          defaultPartOfList = true;
        } else {
          defaultPartOfList = false;
        }
      });


      if (!defaultPartOfList && temp.length > 0) {
        currentSelectedProgram = temp[0].id;
        goto(`/programs/${temp[0].id}`);  
        defaultPartOfList = false;
      }



      
      if (temp.length > 0) {
          filteredPrograms = temp;
          sortPrograms();
          localStorage.setItem('filterParams', JSON.stringify({
          searchText,
          dateFilterType,
          sortCriteria,
          sortOrder,
          startDate,
          endDate
        }));
          goto(`/programs/${filteredPrograms[0].id}`);
          currentSelectedProgram = filteredPrograms[0].id;

      } else {
          searchText = '';
          isProgramFilterOpen = false;
          dateFilterType = 'none';
          sortCriteria = 'name';
          sortOrder = 'dsc';
          startDate = '';
          endDate = '';

          localStorage.setItem('filterParams', JSON.stringify({
          searchText,
          isProgramFilterOpen,
          dateFilterType,
          sortCriteria,
          sortOrder ,
          startDate,
          endDate
        }));
        filteredPrograms = $store;

        toast.info("No results returned.Please try a different search/filter query", {position: "bottom-left"});
      }


      // onResult(temp);
      
    //   store.set(temp);
  };

  const getDateString = () => {
    switch (dateFilterType) {
      case 'last3months':
        return 'Last 3 months';
      case 'last6months':
        return 'Last 6 months';
      case 'last12months':
        return 'Last 12 months';
      case 'after':
        return startDate === '' ? 'No Date Selected' :  startDate.replaceAll('-', '/');
      case 'before':
        return endDate === '' ? 'No Date Selected' : endDate.replaceAll('-', '/');
      default:
        return startDate === '' && endDate === '' ? 'No Date Selected' : `${startDate.replaceAll('-', '/')} - ${endDate.replaceAll('-', '/')}`;
    }
    // dateFilterType === 'after' ? startDate.replaceAll('-', '/') : (dateFilterType === 'before' ? endDate.replaceAll('-', '/') : `${startDate.replaceAll('-', '/')} - ${endDate.replaceAll('-', '/')}`)
  }

  $effect(() => {
    if ($store) {
      filteredPrograms = $store;
    }
  });

  onMount(() => {
    const filterParamsString = localStorage.getItem('filterParams');

    if (!$selectedProgram) {
      selectedProgram.set({value: $store[0].id, label: $store[0].name ||  $store[0].automated_name});
    }

    if (filterParamsString) {
      const filterParams = JSON.parse(filterParamsString);

      searchText = filterParams.searchText;
      dateFilterType = filterParams.dateFilterType;
      sortCriteria = filterParams.sortCriteria;
      sortOrder = filterParams.sortOrder;
      startDate = filterParams.startDate;
      endDate = filterParams.endDate;


      filterPrograms();
    }

    isFillterLoading = false;
  })
</script>

<div class="h-[calc(100vh-190px)] overflow-y-auto" bind:this={containerRef}>
 
  {#if isProgramFilterOpen}
    <div class="w-full h-full">
        
        <div>
          <ProgramFilter 
            bind:isProgramFilterOpen={isProgramFilterOpen}
            searchText={searchText} 
            dateFilterType={dateFilterType}
            sortCriteria={sortCriteria}
            sortOrder={sortOrder}
            startDate={startDate}
            endDate={endDate}
            onFilter={(data: any) => {
              searchText = data.searchText;
              dateFilterType = data.dateFilterType;
              sortCriteria = data.sortCriteria;
              sortOrder = data.sortOrder;
              startDate = data.startDate;
              endDate = data.endDate;
              filterPrograms();
            }}
          />
        </div>
    </div>
  {:else}
    <div class="flex flex-row gap-2 p-2 mb-1 justify-between">
     
      {#if searchText.length > 0}
      <button 
        onclick={() => { isProgramFilterOpen = true}}
        class="cursor-pointer w-[238px] h-[46px] text-[#677489] flex items-center justify-between rounded-md pl-2 flex-1 font-bold bg-white border-2 border-[#000000] ml-0 " 
      >
     
      <span class="mr-4 w-fit" role="link" >
        {searchText}
      </span>
      <X class="text-[#58595c] size-5 mr-2" onclick={(e) => {e.stopPropagation(); searchText = '', filterPrograms();}} />
    </button>
        
      {:else}
      <Button 
      on:click={() => { isProgramFilterOpen = true }} 
      class="cursor-pointer text-[#313da3] font-bold bg-white border-2 border-[#313da3] ml-0 w-[238px] h-[46px] flex items-center" 
    >
      <Search class="text-[#313da3] size-5 mr-2" />
      Search
    </Button>
      {/if}

        
       
     
      <Button 
      variant="default" 
      class="bg-[#FC570C] font-bold text-sm flex items-center gap-2 w-[160px] h-[46px] {isScrolled ? 'fixed bottom-[90px] sm:bottom-4 right-4 md:left-[470px] rounded-full z-50 w-[160px] h-[46px] p-0 justify-center' : ''}" 
      disabled={isLoading} 
      on:click={handleAddProgramClick}
    >
      {#if isLoading}
        <Disc class="animate-spin" />
      {:else}
        <PlusCircle class="size-4" />
      {/if}
      {#if !isScrolled}
        Add Program
      {/if}
    </Button>
    </div>

    <div class="flex flex-wrap gap-1 my-3 px-2">
      {#if dateFilterType !== 'none'}
      <Chip
        sortIcon={dateFilterType === 'after' ? 'asc' : dateFilterType === 'before' ? 'desc' : ''}
        onRemove={() => { 
          dateFilterType = 'none' 
          filterPrograms();
        }}
        title={getDateString()}
    />
    {/if}
      {#if sortCriteria !== ''}
      <Chip
      sortIcon={sortOrder}
        onRemove={() => { 
          sortCriteria = '';
          sortPrograms();
          updateFilterStorage();
        }}
        title={sortCriteria}
      />
      {/if}
    </div>

    <div class="flex flex-col px-2">
      {#if isFillterLoading}
        <div class="flex justify-center items-center mt-8">
          <Disc class="w-10 h-10 text-gray-400 animate-spin" />
        </div>
      {:else}
        {#each filteredPrograms as item, index}
          <div
            class="flex items-center transition transform duration-200 ease-in-out relative w-full mb-1
             mt-1 {currentSelectedProgram === item.id ? 'outline outline-2 outline-blue-500' : ''}"
          >

          {#if item.id === $selectedProgram?.value}
            <div class="absolute top-0 right-0 bg-gray-700 text-white text-[10px] px-2 py-[2px] rounded-bl-sm z-10">
                Default Program
            </div>
          {/if}
            <Card.Root class="rounded-none p-0 w-full">
              <div class="flex justify-between items-center">
                <Card.Header class="w-full p-0">
                  <Card.Title class="w-full text-[#313da3] font-semibold text-[20px] p-1 mb-0">
                    {#if !isDeleteMode}
                      <button
                        onclick={async () => { 
                          isChangingProgram = true;
                          await goto(`/programs/${item.id}`); 
                          isChangingProgram = false;
                          currentSelectedProgram = item.id;  
                          changeTab()
                          
                         }}
                        class="text-black text-[16px] hover:no-underline p-0"
                      >
                        {item.automated_name}
                      </button>
                    {:else}
                      <Button variant="link" disabled class="text-black text-[16px] hover:no-underline p-0">
                        {item.automated_name}
                      </Button>
                    {/if}
                    <Input type="text" 
                      placeholder="{item.name ? item.name : 'Untitled Program'} " 
                      value={programId !== null && Number(item.id) === programId ? programName : item.name}
                      on:keydown={(e: any) => {
                          const n = e.target.value;
                          if (e.key === 'Enter') {
                              handleEditProgramClick(e.target.value, item.id)
                              e.target.value = n;
                              item.name = n
                              e.target.blur()
                          }
                      }}
                      on:click={(e: any) => { e.target.value = item.name}}
                      on:blur={(e: any) => {e.target.value = item.name}}
                      class="border-none placeholder:text-gray-400 shadow-none p-0 py-3 h-4 rounded-none m-0" />
                  </Card.Title>
                </Card.Header>
                      
                {#if $deleteProgramMode}
                   <Button variant="ghost" 
                  class="text-blue-500  text-xl p-0 border-none relative flex items-center justify-center mt-[12px] mr-[6px] hover:bg-transparent" 
                  data-no-dnd="true"
                  on:click={() => { openDeleteDialog = true; currentProgram = item}}>
                     <svg xmlns="http://www.w3.org/2000/svg" class="w-[15px] h-[20px] pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 20L20 4M4 4l16 16"></path></svg>
                </Button>
                {:else}
                <button
                  onclick={async () => { 
                    currentSelectedProgram = item.id;
                    isChangingProgram = true;
                    await goto(`/programs/${item.id}`); 
                    isChangingProgram = false;
                    changeTab();


                  }}
                  class="text-black text-[16px] border-none hover:no-underline p-0 bg-transparent cursor-pointer flex items-center"
                >
                 {#if isChangingProgram && item.id == currentSelectedProgram}
                    <Disc class="animate-spin duration-300 size-4 mr-1"/>
                   
                  {:else}
                    <ChevronRight class="w-4 h-4 mr-1" />
                {/if}
                </button>
                {/if}
              </div>
              <hr class="sm:block hidden">
              <!-- {#if $deleteProgramMode} -->
              <!-- <div class="hidden sm:flex justify-end items-center">
                <Button variant="ghost" 
                  class="text-black text-xl p-0 border-none relative flex items-center justify-center -mt-[1px] mr-[6px] hover:bg-transparent" 
                  data-no-dnd="true"
                  on:click={() => { openDeleteDialog = true; currentProgram = item}}>
                    &times;
                </Button>
              </div> -->
              <!-- {/if} -->
            </Card.Root>

            {#if item.deleting}
              <div class="w-full h-full absolute right-0 flex gap-2 justify-center items-center bg-gray-200">
                <Button class="bg-red-700" disabled={isLoading} on:click={() => { openDeleteDialog = true; currentProgram = item}}>
                  {#if isLoading}
                    <Disc class="mr-2 animate-spin"/>
                    Deleting
                  {:else}
                    <Trash class="mr-2"/>
                    Delete
                  {/if}
                </Button>
                <Button class="" on:click={() => {item.deleting = false}}>
                  <X class="mr-2 size-6"/>
                  Cancel
                </Button>
              </div>
            {/if}
          </div>
        {/each}
    {/if}
      
    </div>
  {/if}


</div>

<Dialog.Root open={openDeleteDialog} closeOnEscape closeOnOutsideClick onOpenChange={(val) => { openDeleteDialog = val }}>
  <Dialog.Content>
    <Dialog.Header>
      <Dialog.Title class="text-center">Delete Program?</Dialog.Title>
      <Dialog.Description class="text-black text-center">
        <p>{currentProgram.automated_name}</p>
        <p class="font-bold">{currentProgram.name}</p>
        <p class="mt-2">(Contains {currentProgram.workouts.length} Workouts)</p>
        <p>This action cannot be undone.</p>
      </Dialog.Description>
    </Dialog.Header>
    <Dialog.Footer>
      <div class="flex flex-row items-center justify-center gap-2 w-full">
        <Button class="bg-red-500 w-full" on:click={handleDelete}>Delete</Button>
        <Button variant="outline" class="w-full text-black" on:click={() => { openDeleteDialog = false; deleteProgramMode.set(false)}}>Cancel</Button>
      </div>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>