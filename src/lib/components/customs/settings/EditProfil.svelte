<script lang="ts">
	import Button from "$lib/components/ui/button/button.svelte";
    import { ChevronLeft, ChevronRight } from "svelte-bootstrap-icons";


    let { showEditProfil = $bindable(false), uData } = $props<{ showEditProfil: boolean, uData: any }>();

    const WEBSITE_URL = import.meta.env.VITE_WEBSITE_URL;


</script>



<div class="md:w-[794px] mx-auto p-2 md:p-5 space-y-8">

    <div class="flex justify-between items-center">
        <button class="" onclick={() => {showEditProfil = false}}>
            <ChevronLeft class="w-4 h-4" />
        </button>
        <h1 class="text-md md:textxl font-semibold">Edit Profile</h1>
        <div></div>
    </div>

    <div class="bg-white p-5 rounded-md space-y-4 shadow-sm">
        <div>
            <h2 class="mb-1">Username</h2>
            <div class="w-full text-[#727272] flex justify-between items-center">
                <span>{uData?.user?.username}</span>
                <a href={`${WEBSITE_URL}/account/edit_profile`} class="underline text-sm font-bold">
                    Edit
                </a>
            </div>
        </div>

        <!-- <div>
            <h2 class="mb-1">First Name</h2>
            <div class="w-full text-[#727272] flex justify-between items-center">
                <span>John</span>
                <a href={`${WEBSITE_URL}/account/edit_profile`} class="underline text-sm font-bold">
                    Edit
                </a>
            </div>
        </div> -->

        <div>
            <h2 class="mb-1">Email Address</h2>
            <div class="w-full text-[#727272] flex justify-between items-center">
                <span>{uData.email}</span>
            </div>
        </div>

        <div class="">
            <h2 class="mb-1">Change Password</h2>

            <div class="space-y-1">
            <a href={`${WEBSITE_URL}/account/edit_profile`} class="w-full text-[#727272] flex justify-between items-center">
               <span class="text-left">Edit Profil and Reset Password</span>
                <span><ChevronRight class="w-4 h-4" /></span>
            </a>
        </div>

        </div>

    </div>

    

       

</div>