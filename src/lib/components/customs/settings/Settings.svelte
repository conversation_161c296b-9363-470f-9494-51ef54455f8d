<script lang="ts">
	import Button from "$lib/components/ui/button/button.svelte";
import { ChevronRight } from "svelte-bootstrap-icons";
	import EditProfil from "./EditProfil.svelte";


let {uData } = $props<{
    uData: any
}>();


const WEBSITE_URL = import.meta.env.VITE_WEBSITE_URL;

const { status, isPremium, email, user } = uData;

let showEditProfil = $state(false);


</script>



{#if showEditProfil}
    <EditProfil bind:showEditProfil uData={uData}/>
{:else}
    <div class="md:w-[794px] mx-auto p-2 md:p-5 space-y-6">
        <h1 class="text-xl font-semibold mb-3">Settings</h1>

        <div class="bg-white p-5 rounded-md space-y-1 shadow-sm">
            <h2 class="font-semibold mb-3">Email</h2>
            <div class="space-y-1">
                <p class="text-[#727272]">{email}</p>
                <button 
                    onclick={() => {showEditProfil = true}}
                    class="w-full text-[#727272] flex justify-between items-center">
                    <span>Edit Profile and Reset Password</span>
                    <span><ChevronRight class="w-4 h-4" /></span>
                </button>
            </div>

        </div>

        {#if status !== 'premium'}
            <div class="bg-white p-5 rounded-md space-y-1 shadow-sm">
                <h2 class="font-semibold mb-3">Account</h2>
                <p class="text-[#727272]">
                    Premium Subscription Membership
                </p>
                <div class="pt-4 flex justify-center">
                    <a href={`${WEBSITE_URL}/premium`} class="bg-[#FD570D] text-white h-[51px] w-[190px] flex items-center justify-center font-semibold rounded-md">
                        Upgrade to Premium
                    </a> 
                </div>

            </div>
        {/if}

        {#if status === 'premium' || status === 'downgraded'}
            <div class="bg-white p-5 rounded-md space-y-1 shadow-sm">
                <h2 class="font-semibold mb-3">Membership and Payment</h2>
                <div class="space-y-1">
                    <a href={`${WEBSITE_URL}/account/billing`} class="w-full text-[#727272] flex justify-between items-center">
                    <span class="text-left">Manage Subscription and Payment Methods</span>
                        <span><ChevronRight class="w-4 h-4" /></span>
                    </a>
                </div>

            </div>
        {/if}

    </div>
{/if}