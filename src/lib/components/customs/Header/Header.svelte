<script lang="ts">
    import { Button } from "$lib/components/ui/button";
    import { List, ChevronLeft, ThreeDotsVertical, ArrowLeftRight } from "svelte-bootstrap-icons";
    import { isProgramTab } from "$lib/stores/generalStore";
    import { get } from "svelte/store";

    const { 
        title = '', 
        subtitle = '', 
        menuIcon = false, 
        backIcon = false,
        originPage = '',
        threeDotsIcon = false,
        defaultTab = $bindable(''),
        onBackIconClick = () => {}, 
        onThreeDotsIconClick = () => {}, 
        onArrowLeftRightClick = () => {},
        onMenuIconClick = () => {} 
    } = $props<{
        title?: string;
        subtitle?: string;
        menuIcon?: boolean;
        backIcon?: boolean;
        originPage?: string;
        threeDotsIcon?: boolean;
        onBackIconClick?: () => void;
        onThreeDotsIconClick?: () => void;
        onArrowLeftRightClick?: () => void;
        onMenuIconClick?: () => void;
    }>();
</script>

<div class="lg:hidden md:hidden flex flex-col p-1 space-y-1">
    <div class="flex justify-between items-center relative">
        <div>
            {#if backIcon && originPage !== ''}
                <Button variant="ghost" class="text-black p-0 pt-5" on:click={onBackIconClick}>
                    <ChevronLeft class="size-6" />
                </Button>
            {/if}
        </div>

        {#if !backIcon && !menuIcon}
            <div></div>
        {/if}
        
        {#if title}
            <div class="absolute left-0 right-0 flex justify-center pointer-events-none">
                {#if $isProgramTab  == false}
                    <h1 class="text-[20px]  text-[#677489] font-semibold">{title}</h1>
                {/if}
            </div>
        {/if}

       
        <div>
            {#if threeDotsIcon && defaultTab !== 'exercises'}
                <Button variant="ghost" class="text-black p-0 pr-3" on:click={onThreeDotsIconClick}>
                    <ThreeDotsVertical class="size-5" />
                </Button>
            {/if}
        </div>
    </div>

    
    <div class="flex justify-between items-center relative">
        
        <div>
            {#if originPage === ''}
                <Button variant="ghost" class="text-black p-0 pl-2" on:click={onArrowLeftRightClick}>
                    <ArrowLeftRight class="size-5" />
                </Button>
            {/if}
        </div>

       
        {#if subtitle}
            <div class="absolute left-0 right-0 flex justify-center pointer-events-none pb-4 pt-2">
                {#if $isProgramTab == false}
                    <p class="text-[20px]  text-black font-semibold">{subtitle}</p>
                {/if}
            </div>
        {/if}

        <div>
            {#if originPage === 'exercises'}
                <Button variant="ghost" class="text-black p-0 pr-3 pb-4 pt-2" on:click={onArrowLeftRightClick}>
                    <ArrowLeftRight class="size-5" />
                </Button>
            {/if}
        </div>
    </div>
</div>

