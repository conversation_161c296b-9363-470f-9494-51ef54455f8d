<script lang="ts">
	import { onMount, onD<PERSON>roy, createEventDispatcher } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { IWorkout, IExercise, IAddExerciseToWorkout, IProgram } from '$lib/typings/index';
	import * as AlertDialog from "$lib/components/ui/alert-dialog";
	import { tick } from 'svelte';
	import { Button } from "$lib/components/ui/button";
	import { ChevronDown, ChevronUp, Disc, Pencil } from "svelte-bootstrap-icons";
	import Card from "$lib/components/ui/card/card.svelte";
	import { current_workout_exercise_id, current_workout_id, exerciseDetails, showExerciseDetailsPage,
		 showMoveExerciseMenuItem, videoToken, selectedProgram, selectedWorkout } from "$lib/stores/generalStore";
	import { writable } from 'svelte/store';
	import { get } from 'svelte/store';
	import { resolveTransformedExercises } from '$lib/utils/tags_exercise.utils';
	import { dndzone, SHADOW_ITEM_MARKER_PROPERTY_NAME } from 'svelte-dnd-action';
	import { updateKanban } from "$lib/utils/workouts_exercises.utils";
	import { flip } from 'svelte/animate';
	import { toast } from 'svelte-sonner';

	export let workoutsStore: Writable<IWorkout[]>;
	export let programId: string;
	export let programName: string;
	export let programAutoName: string;
	export let isDeleteMode: boolean = false;
	export let isEditMode: boolean = false;

	let editingWorkoutId: string | number | null = null;
	let inputRef: HTMLInputElement | null = null;
	let openDeleteWorkoutDialog: boolean = false;

	const collapsedWorkouts = writable(new Map<string, boolean>());

	let showConfirmDialog = false;
	let exerciseToDelete: IExercise | null = null;
	let workoutToDeleteFrom: IWorkout | null = null;
	let workoutToDelete: IWorkout | null = null;
	let defaultWorkout = get(selectedWorkout); 
	let currentSelectedExercise: any = null;

	let isMovingEDetailsPage = false;

	const dispatch = createEventDispatcher<{
		updateData: { workouts: IWorkout[] },
		deleteItem: { workoutId: string | number, exerciseId: string | number | undefined, workoutName: string, exerciseName: string }
	}>();

	onMount(async () => {
		if ($workoutsStore) {
			await tick();
			for (let index = 0; index < $workoutsStore.length; index++) {
				const workout = $workoutsStore[index];
				const newExercises = await resolveTransformedExercises(workout.exercises);
				$workoutsStore[index].exercises = newExercises;

				const workoutIdStr = workout.id.toString();
				if (!get(collapsedWorkouts).has(workoutIdStr)) {
					collapsedWorkouts.update(map => {
						map.set(workoutIdStr, true);
						return new Map(map);
					});
				}

				if (defaultWorkout) {
					if (get(collapsedWorkouts).has(defaultWorkout!.value!.toString())) {
						collapsedWorkouts.update(map => {
							map.set(defaultWorkout!.value!.toString(), false);
							return new Map(map); 
						});
					}
				}
			}
		}
	});

	let container: HTMLDivElement | undefined;
	let isDraggingWorkout = false;
	let isDraggingExercise = false;
	let autoExpandedWorkouts = new Set<string>();
	let hoverTimeout: number | null = null;

	const flipDurationMs = 200;

	$: workoutsForDnd = $workoutsStore?.map((workout, index) => ({
		...workout,
		dndId: `workout-${workout.id}`,
		position: index
	})) || [];

	$: if ($selectedWorkout) {
		defaultWorkout = $selectedWorkout;
	}

	$: if ($workoutsStore && $workoutsStore.length > 0) {
		for (let index = 0; index < $workoutsStore.length; index++) {
			const workout = $workoutsStore[index];
			const workoutIdStr = workout.id.toString();
			if (!get(collapsedWorkouts).has(workoutIdStr)) {
				collapsedWorkouts.update(map => {
					map.set(workoutIdStr, true);
					return new Map(map);
				});
			}

			
			if (defaultWorkout) {

				if (get(collapsedWorkouts).has(defaultWorkout!.value!.toString())) {
					collapsedWorkouts.update(map => {
					map.set(defaultWorkout!.value!.toString(), false);
					return new Map(map); 
					});
				}
			}
		}
	}

	function getExercisesForDnd(workout: IWorkout) {
		if (!workout.exercises || !Array.isArray(workout.exercises)) {
			return [];
		}

		return workout.exercises.map((exercise, index) => ({
			...exercise,
			dndId: `exercise-${workout.id}-${exercise.workout_exercise_id || exercise.id || index}`,
			workoutId: workout.id,
			position: index
		}));
	}

	function handleWorkoutDndConsider(e: CustomEvent) {
		const { items, info } = e.detail;
		if (info.source === 'pointer') {
			isDraggingWorkout = true;
			document.body.classList.add('dragging-workout');
		}
		workoutsStore.set(items.map(({ dndId, position, ...workout }) => workout));
	}

	function handleWorkoutDndFinalize(e: CustomEvent) {
		const { items } = e.detail;
		workoutsStore.set(items.map(({ dndId, position, ...workout }) => workout));
		isDraggingWorkout = false;
		document.body.classList.remove('dragging-workout');
		dispatch('updateData', { workouts: $workoutsStore });
	}

	function trackDragHover(e: PointerEvent) {
		const elements = document.elementsFromPoint(e.clientX, e.clientY);
		const workoutHeader = elements.find(el =>
			el.classList.contains('workout-header') || el.classList.contains('workout-header-content')
		);
		const workoutElement = workoutHeader?.closest<HTMLElement>('.workout');

		if (workoutElement) {
			const workoutId = workoutElement.getAttribute('data-workout-id');
			if (workoutId) {
				handleAutoExpand(workoutId);
			}
		}
	}

	function trackDragHoverMobile(e: TouchEvent) {
		const elements = document.elementsFromPoint(e.touches[0].clientX, e.touches[0].clientY);
		const workoutHeader = elements.find(el =>
			el.classList.contains('workout-header') || el.classList.contains('workout-header-content')
		);
		const workoutElement = workoutHeader?.closest<HTMLElement>('.workout');

		if (workoutElement) {
			const workoutId = workoutElement.getAttribute('data-workout-id');
			if (workoutId) {
				handleAutoExpand(workoutId);
			}
		}
	}


	function handleExerciseDndConsider(workoutId: string | number) {
		return (e: CustomEvent) => {
			const { items, info } = e.detail;

			if (info.source === 'pointer' || info.source === 'touch') {
				isDraggingExercise = true;
				document.body.classList.add('dragging-exercise');
				window.addEventListener('pointermove', trackDragHover);
				window.addEventListener('touchmove', trackDragHoverMobile);
			}

			workoutsStore.update(workouts =>
				workouts.map(workout =>
					workout.id === workoutId
						? {
							...workout,
							exercises: items.map(({ dndId, workoutId, position, ...exercise }) => exercise)
						}
						: workout
				)
			);
		};
	}

	async function handleDataUpdate(event: CustomEvent<{ workouts: IWorkout[] }>, workoutId: string | number ) {
      const updatedWorkoutsFromEvent = event.detail.workouts;
      const itemsToUpdate: IAddExerciseToWorkout[] = [];


	  console.log('event', event.detail.workouts)
      updatedWorkoutsFromEvent.forEach(workout => {
          if (workout.exercises) {
              workout.exercises.forEach((exercise, index) => {  
				 if (typeof exercise.id === 'string' && exercise.id.includes('temp')) {
					exercise.id = undefined;
				}

                  itemsToUpdate.push({
                      exercise: exercise,
                      workout_id: workout.id,
                      position: index + 1
                  });
              });
          }
      });

      try {
          if (itemsToUpdate.length > 0) {
            const data = await updateKanban(itemsToUpdate);

			if (data?.success) {
				await workoutsStore.update(currentWorkouts => {
					return currentWorkouts.map(workout => {
						if (workout.id !== workoutId) {
							return workout;
						}

						console.log('workout', workout.exercises);

						// const exerciseToUpdate = workout.exercises.find(ex => ex.exercise_id.toString() == data.exercise_id);
						// if (!exerciseToUpdate) {
						// 	return workout;
						// }

						const newExercises = workout.exercises.map(exercise =>
							(exercise.exercise_id == data.exercise_id && !exercise.id )
								? { ...exercise, id: data.id }
								: exercise
						);

						return { ...workout, exercises: newExercises };
					});
				});
			}
          }
      } catch (error) {
          console.error("Error updating kanban:", error);
      }
  }


	function handleExerciseDndFinalize(
		workout: IWorkout,
		programId: string,
		programName: string,
		programAutoName: string
		) {
		return (e: CustomEvent) => {
			const { items } = e.detail;

			workoutsStore.update(workouts =>
			workouts.map(w =>
				w.id == workout.id
				? {
					...w,
					exercises: items.map(({ dndId, position, ...exercise }) => ({
						...exercise,
						workoutId: workout.id, 
						position
					}))
					}
				: w
				)
			);

			isDraggingExercise = false;
			document.body.classList.remove('dragging-exercise');

			selectedProgram.set({value: programId, label: programName || programAutoName});
			selectedWorkout.set({value: workout.id, label: workout.name || workout.automated_name});

			window.removeEventListener('pointermove', trackDragHover);
			window.removeEventListener('touchmove', trackDragHoverMobile);
			collapseAutoExpandedWorkouts(workout.id.toString());

			handleDataUpdate(new CustomEvent('updateData', {
				detail: { workouts: $workoutsStore }
			}), workout.id.toString());
		};
	}


	function handleAutoExpand(workoutId: string) {
		if (hoverTimeout) {
			clearTimeout(hoverTimeout);
		}

		hoverTimeout = setTimeout(() => {
			const isCollapsed = $collapsedWorkouts.get(workoutId);
			if (isCollapsed && isDraggingExercise) {
				collapsedWorkouts.update(map => {
					map.set(workoutId, false);
					return new Map(map);
				});
				autoExpandedWorkouts.add(workoutId);
			}
		}, 0); // delay before expanding
	}

	function collapseAutoExpandedWorkouts(workoutID: string | number) {
		autoExpandedWorkouts.forEach(workoutId => {
			if (workoutId !== workoutID.toString()) {
				collapsedWorkouts.update(map => {
					map.set(workoutId, true);
					return new Map(map);
				});
			}
			
		});
		autoExpandedWorkouts.clear();
		
		if (hoverTimeout) {
			clearTimeout(hoverTimeout);
			hoverTimeout = null;
		}
	}

	function confirmDelete(event: MouseEvent, workout: IWorkout, exercise: IExercise) {
		event.stopPropagation();
		workoutToDeleteFrom = workout;
		exerciseToDelete = exercise;
		showConfirmDialog = true;
	}

	function cancelDelete() {
		showConfirmDialog = false;
		exerciseToDelete = null;
		workoutToDeleteFrom = null;
	}

	async function proceedWithDelete() {
		if (exerciseToDelete && workoutToDeleteFrom) {
			dispatch('deleteItem', {
				workoutId: workoutToDeleteFrom.id,
				exerciseId: exerciseToDelete.workout_exercise_id || exerciseToDelete.id,
				workoutName: workoutToDeleteFrom.name || workoutToDeleteFrom.automated_name || `Workout ${workoutToDeleteFrom.id}`,
				exerciseName: exerciseToDelete.name || 'Unknown Exercise'
			});

			workoutsStore.update(workouts =>
				workouts.map(workout =>
					workout.id === workoutToDeleteFrom.id
						? {
							...workout,
							exercises: workout.exercises.filter(ex =>
								(ex.workout_exercise_id || ex.id) !== (exerciseToDelete.workout_exercise_id || exerciseToDelete.id)
							)
						}
						: workout
				)
			);
		}
		cancelDelete();
	}

	async function handleWorkoutDelete() {
		if (workoutToDelete) {
			dispatch('deleteItem', {
				workoutId: workoutToDelete.id,
				exerciseId: undefined,
				workoutName: workoutToDelete.name || workoutToDelete.automated_name || `Workout ${workoutToDelete.id}`,
				exerciseName: ''
			});

			workoutsStore.update(workouts =>
				workouts.filter(workout => workout.id !== workoutToDelete.id)
			);

			openDeleteWorkoutDialog = false;
			workoutToDelete = null;
		}
	}

	function handleWorkoutKeydown(workoutId: string | number, event: KeyboardEvent, newValue: string) {
		if (event.key === 'Enter') {
			saveWorkoutEdit(workoutId, newValue);
		} else if (event.key === 'Escape') {
			editingWorkoutId = null;
		}
	}

	let lastToggleTime = 0;
	const TOGGLE_DEBOUNCE_MS = 300;

	function toggleWorkoutCollapse(workoutId: string | number) {
		const now = Date.now();
		if (now - lastToggleTime < TOGGLE_DEBOUNCE_MS) {
			return;
		}
		lastToggleTime = now;

		if (editingWorkoutId) return;
		const workoutIdStr = workoutId.toString();
		collapsedWorkouts.update(map => {
			if (map.has(workoutIdStr)) {
				map.set(workoutIdStr, !map.get(workoutIdStr));
			}
			return new Map(map);
		});
	}

	async function startEditingWorkout(workoutId: string | number) {
		editingWorkoutId = workoutId;
		await tick();
		inputRef?.focus();
	}

	async function saveWorkoutEdit(workoutId: string | number, newValue: string) {
		if (!$workoutsStore) return;
		const workoutIndex = $workoutsStore.findIndex(w => w.id === workoutId);
		if (workoutIndex !== -1 && newValue.trim()) {
			$workoutsStore[workoutIndex].name = newValue.trim();

			const res = await fetch('/api/workouts/' + $workoutsStore[workoutIndex].id, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ name: newValue.trim() })
			});

			const data = await res.json();

			if (!data.success) {
				toast.error(data.message);
			}

			workoutsStore.update(store => ([...store]));
		}
		editingWorkoutId = null;
	}

	onDestroy(() => {
		if (hoverTimeout) {
			clearTimeout(hoverTimeout);
		}
	});
</script>

<div class="drag-drop-container flex flex-col w-full h-full overflow-auto" bind:this={container}>
	{#if $workoutsStore && $workoutsStore.length > 0}
		<!-- Workout-level DND zone -->
		<div
			class="workout-list"
			use:dndzone={{
				items: $workoutsStore,
				flipDurationMs,
				delayTouchStart: 500,
				dragDisabled: editingWorkoutId !== null || isDraggingExercise,
				morphDisabled: true,
				dropTargetStyle: {
					outline: '2px dashed #4F46E5',
					backgroundColor: 'rgba(79, 70, 229, 0.1)'
				},
			}}
			on:consider={handleWorkoutDndConsider}
			on:finalize={handleWorkoutDndFinalize}
		>
			{#each $workoutsStore as workout (workout.id.toString())}
				{@const isCollapsed = $collapsedWorkouts.get(workout.id.toString())}
				{@const exercisesForDnd = getExercisesForDnd(workout)}
				{@const isShadow = workout[SHADOW_ITEM_MARKER_PROPERTY_NAME]}

				<div
					class="workout {isCollapsed ? 'collapsed' : ''} border border-gray-300 rounded-lg my-2 bg-gray-100"
					data-workout-id={workout.id}
					animate:flip={{ duration: flipDurationMs }}
				>
					<!-- Workout Header -->
					<div
						class="workout-header bg-[#313da3] w-full px-3 py-2 flex items-center justify-between border-b flex-shrink-0"
						data-workout-id={workout.id}
					>
						<div class="workout-header-content flex-1 w-full flex items-center justify-between">
							{#if editingWorkoutId === workout.id}
								
								<input
									bind:this={inputRef}
									type="text"
									class="workout-edit-input w-full px-1 py-0 text-black font-bold"
									value={workout.name || workout.automated_name || ''}
									data-workout-id={workout.id}
									on:keydown={(e) => handleWorkoutKeydown(workout.id, e, (e.target as HTMLInputElement).value)}
									on:blur={() => editingWorkoutId = null}
								/>
							{:else}
							<div
								role="button"
								tabindex="0"
								class="text-white font-bold text-left overflow-hidden text-ellipsis whitespace-nowrap flex-1 cursor-pointer"
							>
								<!-- {programAutoName}: {programName}/{workout.name || workout.automated_name || `Workout ${workout.id}`} -->
									{workout.name || workout.automated_name || `Workout ${workout.id}`}
							</div>
								{#if isDeleteMode}
									<Button variant="ghost" class="relative rounded-full size-8 p-0 text-black" on:click={() => {
										openDeleteWorkoutDialog = true;
										workoutToDelete = workout;
									}}>
										<div class="text-white text-xl">&times;</div>
									</Button>
								{/if}
								{#if isEditMode}
									<Button variant="ghost" class="relative rounded-full size-8 p-0 text-black" on:click={() => {
										startEditingWorkout(workout.id);
									}}>
										<Pencil class="text-white text-xl" />
									</Button>
								{/if}
								<Button
									variant="ghost"
									size="icon"
									class="relative rounded-full size-8 p-2 text-white focus:bg-transparent focus:text-white active:bg-transparent active:text-white chevron-button"
									on:click={(e) => {
										toggleWorkoutCollapse(workout.id);
									}}
								>
									{#if !isCollapsed}
										<ChevronUp />
									{:else}
										<ChevronDown />
									{/if}
									<div class="absolute top-0 right-0 w-full h-full bg-transparent rounded-none"></div>
								</Button>
							{/if}
						</div>
					</div>

					<!-- Exercises Container -->
					<div
						class="exercises-container min-h-[40px] {isCollapsed && !isShadow ? 'hidden' : ''}"
						use:dndzone={{
							items: workout.exercises,
							flipDurationMs,
							dragDisabled: editingWorkoutId !== null || isDraggingWorkout,
							morphDisabled: true,
							delayTouchStart: 500,
							dropTargetStyle: {
								outline: '2px dashed #4F46E5',
								backgroundColor: 'rgba(79, 70, 229, 0.1)'
							},
							dropFromOthersDisabled: false,
							type: 'workout_items'
						}}
						on:consider={handleExerciseDndConsider(workout.id)}
						on:finalize={handleExerciseDndFinalize(workout, programId, programName, programAutoName)}
					>
						{#if workout.exercises && workout.exercises.length > 0}
							{#each workout.exercises as exercise (exercise.id)}
								<div animate:flip={{ duration: flipDurationMs }}>
									<Card
										class="exercise-item w-full h-[95.4px] xs:h-[106px] flex gap-1 mb-1 rounded-none shadow-md bg-white"
										data-exercise-id={exercise.id ?? `fallback-id-${exercise.exercise_id}`}
										data-exercise-name={exercise.name}
										on:click={async () => {
											if (!isDraggingExercise && !isDraggingWorkout) {
												 current_workout_exercise_id.set(exercise.id.toString());
    											current_workout_id.set(workout.id);

												currentSelectedExercise = exercise.id;
												isMovingEDetailsPage = true;
												const response = await fetch(`/api/fetch/${exercise.exercise_id}`);
												const { exercises, token } = await response.json();
												isMovingEDetailsPage = false;

												videoToken.set(token);
												exerciseDetails.set(exercises);
												showExerciseDetailsPage.set(true);
												showMoveExerciseMenuItem.set(true);
											}
										}}
									>
										<div class="w-[108px] xs:w-[120px] h-[95.4px] xs:h-[106px] pl-1 pb-2 pt-2 pr-2">
											<img
												src={exercise.img || 'https://placehold.co/100x100/E89878/31343C?text=No+Img'}
												alt={exercise.name || 'Exercise image'}
												class="w-full h-full object-contain object-fit min-w-[106px] xs:min-w-[120px]"
												draggable="false"
											/>
										</div>

										<div class="flex-grow flex flex-col justify-between text-left pb-0 pl-1 pr-2 overflow-hidden">
											<h3 class="line-clamp-2 text-[18px] xs:text-[20px] font-semibold pr-2 pt-[0.5px] xs:pt-0" style="white-space: normal;">
												{exercise.name || 'Unknown Exercise'}
											</h3>

											<div class="flex-grow"></div>

											{#if exercise.color && exercise.color.length > 0}
												<div class="w-full h-1 flex pr-[7px]">
													{#each exercise.color as color, i (i)}
														<div class="h-[2px] flex-1" style="background-color: {color};"></div>
													{/each}
												</div>
											{/if}

											<div class="flex items-end justify-between w-full pt-2 pb-1">
												<div class="flex gap-1 items-center">
													{#if exercise.icon}
														<img src="{exercise.icon}" alt="" class="size-[18px] xs:size-[20px]" />
													{/if}
													{#if exercise.cat}
														<span class="text-md italic max-w-[200px] truncate">{exercise.cat}</span>
													{/if}
												</div>

												{#if isMovingEDetailsPage && currentSelectedExercise == exercise.id}
													<Disc class="animate-spin duration-300 size-4 mr-1"/>
												{:else}
													<Button
														variant="ghost"
														size="icon"
														class="bg-transparent text-blue-500 py-0 px-0 rounded-none size-[21.6px] xs:size-[24px] delete-icon"
														on:click={(e) => {
															e.stopPropagation();
															confirmDelete(e as MouseEvent, workout, exercise);
														}}
														title="Delete exercise"
													>
														<svg xmlns="http://www.w3.org/2000/svg" class="size-[21.6px] xs:size-[24px] pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 20L20 4M4 4l16 16" />
														</svg>
													</Button>	
												{/if}
											</div>
										</div>
									</Card>
								</div>
							{/each}
						{:else}
							<div class="text-center text-gray-500 py-4">Drop exercises here</div>
						{/if}
					</div>
				</div>
			{/each}
		</div>
	{:else}
		<p class="text-center text-gray-500 p-4">No program data provided or program has no workouts.</p>
	{/if}
</div>

{#if showConfirmDialog && exerciseToDelete && workoutToDeleteFrom}
	<div class="confirmation-dialog-overlay">
		<div class="confirmation-dialog">
			<h3>Confirm Deletion</h3>
			<p>
				Delete exercise "{exerciseToDelete.name || 'Unnamed Exercise'}" from workout "{workoutToDeleteFrom.name || workoutToDeleteFrom.automated_name || `Workout ${workoutToDeleteFrom.id}`}"? This cannot be undone through the UI.
			</p>
			<div class="dialog-buttons">
				<button class="cancel-btn" on:click={cancelDelete}>Cancel</button>
				<button class="delete-btn" on:click={proceedWithDelete}>Delete</button>
			</div>
		</div>
	</div>
{/if}

<AlertDialog.Root
	open={openDeleteWorkoutDialog}
	closeOnEscape
	closeOnOutsideClick
	onOpenChange={() => { openDeleteWorkoutDialog = !openDeleteWorkoutDialog }}
>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>Confirm Deletion</AlertDialog.Title>
			<AlertDialog.Description>
				Once deleted, <b>{workoutToDelete?.name || workoutToDelete?.automated_name}</b> cannot be recovered. Proceed?
			</AlertDialog.Description>
		</AlertDialog.Header>
		<AlertDialog.Footer>
			<AlertDialog.Cancel on:click={() => {
				openDeleteWorkoutDialog = false;
				isDeleteMode = false;
				workoutToDelete = null;
			}}>Cancel</AlertDialog.Cancel>
			<AlertDialog.Action class="bg-red-500" on:click={handleWorkoutDelete}> Delete </AlertDialog.Action>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>

<style>
	:global(.dragging-workout) {
		cursor: grabbing !important;
	}

	:global(.dragging-exercise) {
		cursor: grabbing !important;
	}
	:global(.chevron-button) {
		touch-action: manipulation !important;
		pointer-events: auto !important;
	}

	:global(.workout-header.header-drop-target) {
		background-color: rgba(79, 70, 229, 0.2) !important;
		border: 2px dashed #4F46E5 !important;
	}

	:global(.exercises-container.drop-target) {
		background-color: rgba(79, 70, 229, 0.1) !important;
		border: 2px dashed #4F46E5 !important;
	}

	:global(.exercises-container.drop-active) {
		background-color: rgba(79, 70, 229, 0.05) !important;
	}

	.collapsed .exercises-container {
		display: none;
	}

	.confirmation-dialog-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.confirmation-dialog {
		background: white;
		padding: 20px;
		border-radius: 8px;
		max-width: 400px;
		width: 90%;
	}

	.confirmation-dialog h3 {
		margin-top: 0;
		margin-bottom: 15px;
		font-size: 1.2em;
		font-weight: bold;
	}

	.confirmation-dialog p {
		margin-bottom: 20px;
	}

	.dialog-buttons {
		display: flex;
		gap: 10px;
		justify-content: flex-end;
	}

	.cancel-btn, .delete-btn {
		padding: 8px 16px;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-weight: 500;
	}

	.cancel-btn {
		background-color: #e0e0e0;
		color: #333;
	}

	.cancel-btn:hover {
		background-color: #d0d0d0;
	}

	.delete-btn {
		background-color: #dc3545;
		color: white;
	}

	.delete-btn:hover {
		background-color: #c82333;
	}
</style>