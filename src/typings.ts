export interface IUser {
    uid: number | null;
    cid: number;
    username: string;
}

export interface IAuth {
    userId: string | null;
    refresh_token: string | null
    access_token: string | null
    isAuthenticated: boolean;
}

export interface ICard {
    id: number;
    name: string;
}

export interface IProgram {
    id: number;
    name: string;
    // description: string;
    userId: number;
    createdAt: Date;
    updatedAt: Date;
}

export interface IWorkout {
    id: number;
    name: string;
    // description: string;
    // duration: number;
    // calories: number;
    userId: number;
    programId: number;
    createdAt: Date;
}

export interface IExercise {
    id: number;
    apiExerciseId: number;
    name: string;
    userId: number;
    workoutId: number;
    createdAt: Date;
    updatedAt: Date;
}