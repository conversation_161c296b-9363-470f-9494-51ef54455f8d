import * as Sentry from "@sentry/sveltekit";


Sentry.init({
  dsn: "https://<EMAIL>/4508704131579904",

  tracesSampleRate: 1.0,
  integrations: [Sentry.replayIntegration()],
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
  transportOptions: {
    fetchOptions: {
        mode: 'cors',
        credentials: 'same-origin',
    }
  }
});


export const handleError = Sentry.handleErrorWithSentry();