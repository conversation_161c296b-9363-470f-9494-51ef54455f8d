{"name": "my-app", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write ."}, "devDependencies": {"@smui/button": "^7.0.0", "@smui/paper": "^7.0.0", "@smui/tab": "^7.0.0", "@smui/tab-bar": "^7.0.0", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@types/axios": "^0.14.0", "@types/eslint": "^8.56.7", "autoprefixer": "^10.4.19", "bits-ui": "^0.22.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "postcss": "^8.4.39", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.1.2", "svelte": "^5.0.0-next.1", "svelte-bootstrap-icons": "^3.1.1", "svelte-check": "^3.6.0", "svelte-dnd-action": "^0.9.49", "tailwindcss": "^3.4.4", "tslib": "^2.4.1", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0-alpha.20", "vite": "^5.0.3"}, "type": "module", "dependencies": {"@interactjs/types": "^1.10.27", "@material-tailwind/html": "^2.2.2", "@sentry/sveltekit": "^9.35.0", "@sveltejs/adapter-node": "^5.2.5", "@sveltestrap/sveltestrap": "^6.2.7", "axios": "^1.7.4", "clsx": "^2.1.1", "hls.js": "^1.5.17", "interactjs": "^1.10.27", "lru-cache": "^11.1.0", "mode-watcher": "^0.4.1", "moment": "^2.30.1", "svelte-hamburgers": "^5.0.0", "svelte-sonner": "^0.3.28", "tailwind-merge": "^2.4.0", "tailwind-variants": "^0.2.1", "uuid": "^10.0.0", "vaul-svelte": "^0.3.2"}}